#!/usr/bin/env python3
"""
直接测试修复
"""

import sys
import os
import importlib

# 清除缓存
if 'src.data_loader' in sys.modules:
    del sys.modules['src.data_loader']
if 'src' in sys.modules:
    del sys.modules['src']

sys.path.insert(0, 'src')

try:
    from data_loader import DataLoader
    import config
    
    print("🔍 直接测试数据验证修复")
    print("=" * 50)
    
    # 初始化DataLoader
    loader = DataLoader()
    print(f"找到 {len(loader.file_list)} 个文件")
    
    # 只使用前3个文件测试
    loader.file_list = loader.file_list[:3]
    
    # 加载数据
    print("加载数据...")
    data = loader.load_all_data()
    print(f"数据形状: {data.shape}")
    print(f"索引类型: {type(data.index)}")
    print(f"索引名称: {data.index.names}")
    print(f"列名: {list(data.columns)}")
    
    # 测试验证
    print("\n测试验证...")
    is_valid = loader.validate_data(data)
    
    if is_valid:
        print("✅ 验证通过！")
    else:
        print("❌ 验证失败")
        
        # 调试信息
        required_columns = config.DATA_VALIDATION['required_columns']
        available_columns = set(data.columns)
        if isinstance(data.index, pd.MultiIndex):
            available_columns.update(data.index.names)
        elif data.index.name:
            available_columns.add(data.index.name)
        
        print(f"必需列: {required_columns}")
        print(f"可用列: {available_columns}")
        print(f"缺少列: {set(required_columns) - available_columns}")

except Exception as e:
    print(f"错误: {str(e)}")
    import traceback
    traceback.print_exc()
