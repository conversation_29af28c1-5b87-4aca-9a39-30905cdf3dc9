#!/usr/bin/env python3
"""
Visualizer模块演示脚本
展示数据可视化功能的完整使用
"""

import sys
import os
sys.path.append('src')

from data_loader import DataLoader
from time_window_analyzer import TimeWindowAnalyzer
from ranking_analyzer import RankingAnalyzer
from visualizer import Visualizer
import config
import pandas as pd

def main():
    """主演示函数"""
    print("=" * 90)
    print("A股板块数据分析程序 - Visualizer模块演示")
    print("=" * 90)
    
    try:
        # 1. 加载数据
        print("\n1. 加载演示数据...")
        loader = DataLoader()
        # 加载前50个文件进行演示
        loader.file_list = loader.file_list[:50]
        data = loader.load_all_data()
        print(f"   ✓ 数据加载完成，形状: {data.shape}")
        
        # 2. 执行各种分析
        print("\n2. 执行数据分析...")
        
        # 时间窗口分析
        print("   执行时间窗口分析...")
        window_analyzer = TimeWindowAnalyzer(data)
        end_date = window_analyzer.available_dates[-1]
        
        window_results = {}
        for days in [7, 14, 30]:
            performance = window_analyzer.calculate_window_performance(end_date, days)
            if not performance.empty:
                window_results[days] = performance
                print(f"     ✓ {days}日窗口: {len(performance)} 个板块")
        
        # 排名分析
        print("   执行排名统计分析...")
        ranking_analyzer = RankingAnalyzer(data)
        champions = ranking_analyzer.count_daily_champions()
        ranking_frequency = ranking_analyzer.count_top10_frequency(window_days=30)
        
        print(f"     ✓ 冠军统计: {len(champions)} 个板块")
        print(f"     ✓ 前10频次: {len(ranking_frequency)} 个板块")
        
        # 3. 初始化可视化器
        print("\n3. 初始化数据可视化器...")
        visualizer = Visualizer(output_dir='demo_charts')
        print(f"   ✓ 可视化器初始化完成")
        
        chart_info = visualizer.get_chart_info()
        print(f"   ✓ 输出目录: {chart_info['output_dir']}")
        print(f"   ✓ 图表配置: {chart_info['chart_config']['figsize']}, DPI: {chart_info['chart_config']['dpi']}")
        
        # 4. 生成时间窗口表现图表
        print("\n4. 生成时间窗口表现图表...")
        window_charts = []
        
        for days, performance in window_results.items():
            print(f"   生成 {days} 日时间窗口图表...")
            title = f"A股板块{days}日时间窗口表现分析 - 演示版"
            filepath = visualizer.plot_window_performance(performance, days, 
                                                        top_n=15, title=title)
            if filepath:
                window_charts.append(filepath)
                print(f"     ✓ 图表已生成: {os.path.basename(filepath)}")
                
                # 显示前3名板块
                top3 = performance.head(3)
                print(f"     📊 前3名板块:")
                for idx, row in top3.iterrows():
                    print(f"       {idx+1}. {row['sector_name']}: {row['cumulative_return_pct']:.2f}%")
        
        # 5. 生成排名频次图表
        print("\n5. 生成排名频次统计图表...")
        if not ranking_frequency.empty:
            title = "A股板块排名频次统计分析 - 演示版"
            filepath = visualizer.plot_ranking_frequency(ranking_frequency, 
                                                       top_n=15, title=title)
            if filepath:
                print(f"   ✓ 排名频次图表已生成: {os.path.basename(filepath)}")
                
                # 显示前3名板块
                top3 = ranking_frequency.head(3)
                print(f"   📈 前10频次前3名:")
                for idx, row in top3.iterrows():
                    print(f"     {idx+1}. {row['sector_name']}: {row['top10_count']}次 "
                          f"({row['top10_frequency_pct']:.1f}%)")
        
        # 6. 生成冠军分布图表
        print("\n6. 生成冠军分布统计图表...")
        if not champions.empty:
            title = "A股板块冠军分布统计分析 - 演示版"
            filepath = visualizer.plot_champion_distribution(champions, 
                                                           top_n=12, title=title)
            if filepath:
                print(f"   ✓ 冠军分布图表已生成: {os.path.basename(filepath)}")
                
                # 显示前3名板块
                top3 = champions.head(3)
                print(f"   🏆 冠军次数前3名:")
                for idx, row in top3.iterrows():
                    print(f"     {idx+1}. {row['sector_name']}: {row['champion_count']}次 "
                          f"({row['champion_frequency_pct']:.2f}%)")
        
        # 7. 生成时间序列趋势图表
        print("\n7. 生成时间序列趋势图表...")
        if not champions.empty:
            # 选择前6个冠军板块
            top_sectors = champions.head(6)['sector_code'].tolist()
            sector_names = champions.head(6)['sector_name'].tolist()
            
            print(f"   选择板块: {', '.join(sector_names)}")
            
            title = "A股板块时间序列趋势分析 - 演示版"
            filepath = visualizer.plot_time_series_trends(data, top_sectors, 
                                                        metric='change_pct', title=title)
            if filepath:
                print(f"   ✓ 时间序列图表已生成: {os.path.basename(filepath)}")
        
        # 8. 生成综合分析报告
        print("\n8. 生成综合可视化报告...")
        
        # 准备综合数据
        all_data = {
            'window_performance': window_results,
            'champions': champions,
            'ranking_frequency': ranking_frequency,
            'raw_data': data,
            'top_sectors': champions.head(6)['sector_code'].tolist() if not champions.empty else []
        }
        
        report_title = "A股板块数据分析综合报告 - 演示版"
        generated_files = visualizer.generate_comprehensive_report(all_data, report_title)
        
        print(f"   ✓ 综合报告生成完成，共生成 {len(generated_files)} 个图表文件")
        
        # 9. 展示生成结果
        print("\n9. 生成结果总览...")
        output_dir = visualizer.output_dir
        
        if output_dir.exists():
            all_files = list(output_dir.glob("*.png"))
            total_size = sum(f.stat().st_size for f in all_files) / 1024 / 1024  # MB
            
            print(f"   📁 输出目录: {output_dir}")
            print(f"   📊 图表文件总数: {len(all_files)}")
            print(f"   💾 总文件大小: {total_size:.2f} MB")
            
            print(f"\n   📋 生成的图表文件:")
            print("   " + "-" * 70)
            print("   文件名                                    大小(KB)   类型")
            print("   " + "-" * 70)
            
            for file in sorted(all_files, key=lambda x: x.stat().st_mtime, reverse=True):
                file_size = file.stat().st_size / 1024
                file_type = "综合报告" if "comprehensive" in file.name else \
                           "时间窗口" if "window" in file.name else \
                           "排名频次" if "ranking" in file.name else \
                           "冠军分布" if "champion" in file.name else \
                           "时间序列" if "time_series" in file.name else "其他"
                
                print(f"   {file.name:<35} {file_size:8.1f}   {file_type}")
            
            print("   " + "-" * 70)
        
        # 10. 功能特性总结
        print(f"\n10. 可视化功能特性总结...")
        print("   " + "=" * 70)
        print("   🎨 图表类型:")
        print("     • 时间窗口表现图 (柱状图 + 散点图)")
        print("     • 排名频次统计图 (多维度柱状图 + 散点图)")
        print("     • 冠军分布图 (饼图 + 柱状图 + 箱线图)")
        print("     • 时间序列趋势图 (线图 + 累计收益图)")
        print("     • 综合摘要图 (多图表组合)")
        print()
        print("   ✨ 技术特性:")
        print("     • 中文字体自动适配")
        print("     • 专业配色方案")
        print("     • 高分辨率输出")
        print("     • 自动布局优化")
        print("     • 数据标签显示")
        print("     • 图例和颜色条")
        print("     • 网格和样式美化")
        print("   " + "=" * 70)
        
        print("\n" + "=" * 90)
        print("演示完成！Visualizer模块功能展示结束。")
        print(f"所有图表文件已保存到: {output_dir}")
        print("=" * 90)
        
    except Exception as e:
        print(f"\n演示过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
