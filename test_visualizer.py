#!/usr/bin/env python3
"""
Visualizer模块测试脚本
"""

import sys
import os
sys.path.append('src')

from data_loader import DataLoader
from time_window_analyzer import TimeWindowAnalyzer
from ranking_analyzer import RankingAnalyzer
from visualizer import Visualizer
import config
import pandas as pd

def test_visualizer():
    """测试Visualizer功能"""
    print("=" * 80)
    print("Visualizer模块测试")
    print("=" * 80)
    
    try:
        # 1. 加载测试数据
        print("1. 加载测试数据...")
        loader = DataLoader()
        # 只加载前30个文件进行测试
        loader.file_list = loader.file_list[:30]
        data = loader.load_all_data()
        print(f"   ✓ 数据加载完成，形状: {data.shape}")
        
        # 2. 准备分析数据
        print("\n2. 准备分析数据...")
        
        # 时间窗口分析
        window_analyzer = TimeWindowAnalyzer(data)
        end_date = window_analyzer.available_dates[-1]
        window_performance_7d = window_analyzer.calculate_window_performance(end_date, 7)
        window_performance_14d = window_analyzer.calculate_window_performance(end_date, 14)
        
        # 排名分析
        ranking_analyzer = RankingAnalyzer(data)
        champions = ranking_analyzer.count_daily_champions()
        ranking_frequency = ranking_analyzer.count_top10_frequency(window_days=20)
        
        print(f"   ✓ 7日窗口数据: {len(window_performance_7d)} 个板块")
        print(f"   ✓ 14日窗口数据: {len(window_performance_14d)} 个板块")
        print(f"   ✓ 冠军数据: {len(champions)} 个板块")
        print(f"   ✓ 排名频次数据: {len(ranking_frequency)} 个板块")
        
        # 3. 初始化Visualizer
        print("\n3. 初始化Visualizer...")
        visualizer = Visualizer(output_dir='test_output')
        print(f"   ✓ 可视化器初始化完成")
        
        chart_info = visualizer.get_chart_info()
        print(f"   ✓ 输出目录: {chart_info['output_dir']}")
        print(f"   ✓ 支持格式: {chart_info['supported_formats']}")
        
        # 4. 测试时间窗口表现图
        print("\n4. 测试时间窗口表现图...")
        if not window_performance_7d.empty:
            filepath = visualizer.plot_window_performance(window_performance_7d, 7, 
                                                        top_n=10, title="测试-7日窗口表现")
            if filepath:
                print(f"   ✓ 7日窗口表现图已生成: {os.path.basename(filepath)}")
            else:
                print("   ✗ 7日窗口表现图生成失败")
        
        if not window_performance_14d.empty:
            filepath = visualizer.plot_window_performance(window_performance_14d, 14, 
                                                        top_n=10, title="测试-14日窗口表现")
            if filepath:
                print(f"   ✓ 14日窗口表现图已生成: {os.path.basename(filepath)}")
            else:
                print("   ✗ 14日窗口表现图生成失败")
        
        # 5. 测试排名频次图
        print("\n5. 测试排名频次图...")
        if not ranking_frequency.empty:
            filepath = visualizer.plot_ranking_frequency(ranking_frequency, 
                                                       top_n=10, title="测试-排名频次统计")
            if filepath:
                print(f"   ✓ 排名频次图已生成: {os.path.basename(filepath)}")
            else:
                print("   ✗ 排名频次图生成失败")
        
        # 6. 测试冠军分布图
        print("\n6. 测试冠军分布图...")
        if not champions.empty:
            filepath = visualizer.plot_champion_distribution(champions, 
                                                           top_n=8, title="测试-冠军分布统计")
            if filepath:
                print(f"   ✓ 冠军分布图已生成: {os.path.basename(filepath)}")
            else:
                print("   ✗ 冠军分布图生成失败")
        
        # 7. 测试时间序列趋势图
        print("\n7. 测试时间序列趋势图...")
        if not champions.empty:
            # 选择前5个冠军板块
            top_sectors = champions.head(5)['sector_code'].tolist()
            filepath = visualizer.plot_time_series_trends(data, top_sectors, 
                                                        metric='change_pct', 
                                                        title="测试-时间序列趋势")
            if filepath:
                print(f"   ✓ 时间序列趋势图已生成: {os.path.basename(filepath)}")
            else:
                print("   ✗ 时间序列趋势图生成失败")
        
        # 8. 测试综合报告生成
        print("\n8. 测试综合报告生成...")
        all_data = {
            'window_performance': {
                7: window_performance_7d,
                14: window_performance_14d
            },
            'champions': champions,
            'ranking_frequency': ranking_frequency,
            'raw_data': data,
            'top_sectors': champions.head(6)['sector_code'].tolist() if not champions.empty else []
        }
        
        generated_files = visualizer.generate_comprehensive_report(all_data, 
                                                                 "测试综合报告")
        print(f"   ✓ 综合报告生成完成，共 {len(generated_files)} 个文件:")
        for i, filepath in enumerate(generated_files, 1):
            print(f"     {i}. {os.path.basename(filepath)}")
        
        # 9. 检查输出文件
        print("\n9. 检查输出文件...")
        output_dir = visualizer.output_dir
        if output_dir.exists():
            output_files = list(output_dir.glob("*.png"))
            print(f"   ✓ 输出目录存在: {output_dir}")
            print(f"   ✓ 生成图片文件数: {len(output_files)}")
            
            if output_files:
                print("   生成的文件:")
                for file in sorted(output_files):
                    file_size = file.stat().st_size / 1024  # KB
                    print(f"     - {file.name} ({file_size:.1f} KB)")
        
        print("\n" + "=" * 80)
        print("测试完成！Visualizer模块工作正常。")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        print(f"\n测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    test_visualizer()
