#!/usr/bin/env python3
"""
主程序测试脚本
"""

import subprocess
import sys
import os
from pathlib import Path

def test_main_program():
    """测试主程序功能"""
    print("=" * 80)
    print("主程序集成测试")
    print("=" * 80)
    
    # 测试用例列表
    test_cases = [
        {
            'name': '帮助信息测试',
            'args': ['--help'],
            'expect_success': True,
            'description': '测试命令行帮助信息显示'
        },
        {
            'name': '快速模式测试',
            'args': ['--quick-mode', '--verbose', '--no-charts', '--top-n', '5'],
            'expect_success': True,
            'description': '测试快速模式，不生成图表，显示前5名'
        },
        {
            'name': '限制文件数测试',
            'args': ['--max-files', '20', '--windows', '7,14', '--no-charts'],
            'expect_success': True,
            'description': '测试限制文件数量，只分析7日和14日窗口'
        },
        {
            'name': '完整功能测试',
            'args': ['--windows', '7', '--top-n', '3', '--export-csv', '--output-dir', 'test_output'],
            'expect_success': True,
            'description': '测试完整功能：7日窗口，前3名，导出CSV，生成图表'
        },
        {
            'name': '错误参数测试',
            'args': ['--top-n', '0'],
            'expect_success': False,
            'description': '测试错误参数处理'
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print(f"   描述: {test_case['description']}")
        print(f"   参数: {' '.join(test_case['args'])}")
        
        try:
            # 构建命令
            cmd = [sys.executable, 'main.py'] + test_case['args']
            
            # 执行命令
            if test_case['args'] == ['--help']:
                # 帮助信息测试
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                success = result.returncode == 0
            else:
                # 其他测试
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
                success = (result.returncode == 0) == test_case['expect_success']
            
            if success:
                print(f"   结果: ✅ 通过")
                if result.stdout and len(result.stdout) > 100:
                    print(f"   输出: {result.stdout[:100]}...")
                elif result.stdout:
                    print(f"   输出: {result.stdout}")
            else:
                print(f"   结果: ❌ 失败")
                if result.stderr:
                    print(f"   错误: {result.stderr[:200]}...")
                if result.stdout:
                    print(f"   输出: {result.stdout[:200]}...")
            
            results.append({
                'name': test_case['name'],
                'success': success,
                'return_code': result.returncode,
                'stdout_length': len(result.stdout) if result.stdout else 0,
                'stderr_length': len(result.stderr) if result.stderr else 0
            })
            
        except subprocess.TimeoutExpired:
            print(f"   结果: ⏰ 超时")
            results.append({
                'name': test_case['name'],
                'success': False,
                'return_code': -1,
                'stdout_length': 0,
                'stderr_length': 0
            })
            
        except Exception as e:
            print(f"   结果: ❌ 异常 - {str(e)}")
            results.append({
                'name': test_case['name'],
                'success': False,
                'return_code': -2,
                'stdout_length': 0,
                'stderr_length': 0
            })
    
    # 输出测试总结
    print("\n" + "=" * 80)
    print("测试总结")
    print("=" * 80)
    
    passed = sum(1 for r in results if r['success'])
    total = len(results)
    
    print(f"总测试数: {total}")
    print(f"通过数量: {passed}")
    print(f"失败数量: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    print(f"\n详细结果:")
    print("-" * 60)
    print(f"{'测试名称':<20} {'结果':<6} {'返回码':<6} {'输出长度':<8} {'错误长度':<8}")
    print("-" * 60)
    
    for result in results:
        status = "通过" if result['success'] else "失败"
        print(f"{result['name']:<20} {status:<6} {result['return_code']:<6} "
              f"{result['stdout_length']:<8} {result['stderr_length']:<8}")
    
    print("-" * 60)
    
    # 检查输出文件
    print(f"\n输出文件检查:")
    output_dirs = ['output', 'test_output']
    
    for output_dir in output_dirs:
        if Path(output_dir).exists():
            files = list(Path(output_dir).glob("*"))
            print(f"  {output_dir}/: {len(files)} 个文件")
            
            # 显示最近的几个文件
            if files:
                recent_files = sorted(files, key=lambda x: x.stat().st_mtime, reverse=True)[:3]
                for file in recent_files:
                    size_kb = file.stat().st_size / 1024
                    print(f"    - {file.name} ({size_kb:.1f} KB)")
        else:
            print(f"  {output_dir}/: 目录不存在")
    
    print("\n" + "=" * 80)
    if passed == total:
        print("🎉 所有测试通过！主程序集成成功。")
    else:
        print(f"⚠️ {total - passed} 个测试失败，需要检查问题。")
    print("=" * 80)
    
    return passed == total

if __name__ == '__main__':
    test_main_program()
