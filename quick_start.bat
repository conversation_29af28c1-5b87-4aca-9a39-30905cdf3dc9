@echo off
chcp 65001 >nul
echo ========================================
echo A股板块数据分析程序 - 快速启动
echo ========================================
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo 请先安装Python 3.7+
    pause
    exit /b 1
)

echo ✅ Python环境正常

echo.
echo 📦 安装依赖包...
python -m pip install -r requirements.txt
if errorlevel 1 (
    echo ⚠️ 依赖包安装可能有问题，但继续尝试运行
)

echo.
echo 🚀 启动分析程序（快速模式）...
echo.
python main.py --quick-mode --verbose --top-n 8

echo.
echo 🎉 分析完成！请查看output目录中的结果文件。
echo.
pause
