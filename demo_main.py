#!/usr/bin/env python3
"""
主程序演示脚本
展示A股板块数据分析程序的完整功能
"""

import subprocess
import sys
import os
import time
from pathlib import Path

def run_demo_command(description, args, timeout=180):
    """运行演示命令"""
    print(f"\n{'='*80}")
    print(f"演示: {description}")
    print(f"{'='*80}")
    print(f"命令: python main.py {' '.join(args)}")
    print(f"超时: {timeout} 秒")
    print("-" * 80)
    
    try:
        start_time = time.time()
        
        # 执行命令
        cmd = [sys.executable, 'main.py'] + args
        result = subprocess.run(cmd, text=True, timeout=timeout)
        
        execution_time = time.time() - start_time
        
        if result.returncode == 0:
            print(f"✅ 执行成功")
            print(f"⏱️ 执行时间: {execution_time:.2f} 秒")
        else:
            print(f"❌ 执行失败，返回码: {result.returncode}")
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print(f"⏰ 执行超时 ({timeout} 秒)")
        return False
    except Exception as e:
        print(f"❌ 执行异常: {str(e)}")
        return False

def main():
    """主演示函数"""
    print("🚀 A股板块数据分析程序 - 完整功能演示")
    print("=" * 80)
    print("本演示将展示程序的各种功能和参数组合")
    print("请确保data目录中有足够的数据文件")
    print("=" * 80)
    
    # 检查环境
    print("\n📋 环境检查:")
    print("-" * 40)
    
    # 检查主程序文件
    if not Path('main.py').exists():
        print("❌ main.py 文件不存在")
        return False
    print("✅ main.py 文件存在")
    
    # 检查数据目录
    if not Path('data').exists():
        print("❌ data 目录不存在")
        return False
    
    data_files = list(Path('data').glob('*.csv'))
    print(f"✅ data 目录存在，包含 {len(data_files)} 个CSV文件")
    
    if len(data_files) < 10:
        print("⚠️ 数据文件较少，建议至少有10个文件以获得更好的演示效果")
    
    # 检查src目录
    if not Path('src').exists():
        print("❌ src 目录不存在")
        return False
    print("✅ src 目录存在")
    
    # 演示用例列表
    demos = [
        {
            'description': '显示帮助信息',
            'args': ['--help'],
            'timeout': 10
        },
        {
            'description': '快速模式演示 - 基础分析',
            'args': ['--quick-mode', '--verbose', '--top-n', '5'],
            'timeout': 120
        },
        {
            'description': '限制文件数演示 - 只分析最近30个文件',
            'args': ['--max-files', '30', '--windows', '7,14', '--verbose'],
            'timeout': 150
        },
        {
            'description': '单一时间窗口分析 - 7日窗口',
            'args': ['--windows', '7', '--top-n', '8', '--no-charts', '--verbose'],
            'timeout': 120
        },
        {
            'description': '多时间窗口对比 - 7日、14日、30日',
            'args': ['--windows', '7,14,30', '--top-n', '6', '--no-charts'],
            'timeout': 180
        },
        {
            'description': '完整分析 - 包含图表生成',
            'args': ['--windows', '14', '--top-n', '10', '--output-dir', 'demo_output'],
            'timeout': 200
        },
        {
            'description': '数据导出演示 - 生成CSV文件',
            'args': ['--quick-mode', '--windows', '7,14', '--export-csv', '--output-dir', 'demo_export'],
            'timeout': 150
        }
    ]
    
    # 执行演示
    success_count = 0
    total_count = len(demos)
    
    for i, demo in enumerate(demos, 1):
        print(f"\n🎯 演示 {i}/{total_count}")
        
        success = run_demo_command(
            demo['description'],
            demo['args'],
            demo['timeout']
        )
        
        if success:
            success_count += 1
        
        # 检查输出文件（如果有）
        if '--output-dir' in demo['args']:
            output_dir_idx = demo['args'].index('--output-dir') + 1
            if output_dir_idx < len(demo['args']):
                output_dir = demo['args'][output_dir_idx]
                if Path(output_dir).exists():
                    files = list(Path(output_dir).glob('*'))
                    print(f"📁 输出文件: {len(files)} 个")
                    
                    # 显示文件类型统计
                    file_types = {}
                    total_size = 0
                    for file in files:
                        ext = file.suffix.lower()
                        file_types[ext] = file_types.get(ext, 0) + 1
                        total_size += file.stat().st_size
                    
                    print(f"📊 文件类型: {dict(file_types)}")
                    print(f"💾 总大小: {total_size / 1024 / 1024:.2f} MB")
        
        # 短暂暂停
        if i < total_count:
            time.sleep(2)
    
    # 演示总结
    print(f"\n{'='*80}")
    print("🎉 演示总结")
    print(f"{'='*80}")
    
    print(f"📊 执行统计:")
    print(f"   总演示数: {total_count}")
    print(f"   成功数量: {success_count}")
    print(f"   失败数量: {total_count - success_count}")
    print(f"   成功率: {success_count/total_count*100:.1f}%")
    
    # 检查所有输出目录
    print(f"\n📁 输出目录检查:")
    output_dirs = ['output', 'demo_output', 'demo_export']
    
    for output_dir in output_dirs:
        if Path(output_dir).exists():
            files = list(Path(output_dir).glob('*'))
            if files:
                total_size = sum(f.stat().st_size for f in files) / 1024 / 1024
                print(f"   {output_dir}/: {len(files)} 个文件, {total_size:.2f} MB")
                
                # 显示最新的3个文件
                recent_files = sorted(files, key=lambda x: x.stat().st_mtime, reverse=True)[:3]
                for file in recent_files:
                    size_kb = file.stat().st_size / 1024
                    print(f"     - {file.name} ({size_kb:.1f} KB)")
            else:
                print(f"   {output_dir}/: 空目录")
        else:
            print(f"   {output_dir}/: 不存在")
    
    # 功能特性总结
    print(f"\n✨ 程序功能特性:")
    print("   🔍 数据分析:")
    print("     • 时间窗口累计涨跌幅分析")
    print("     • 板块排名频次统计")
    print("     • 单日冠军板块统计")
    print("   📊 可视化:")
    print("     • 时间窗口表现图表")
    print("     • 排名频次统计图表")
    print("     • 冠军分布图表")
    print("     • 时间序列趋势图表")
    print("   💾 数据导出:")
    print("     • CSV格式详细数据")
    print("     • 高分辨率图表文件")
    print("   ⚙️ 灵活配置:")
    print("     • 自定义时间窗口")
    print("     • 可调节显示数量")
    print("     • 快速模式和限制模式")
    print("     • 详细输出控制")
    
    print(f"\n{'='*80}")
    if success_count == total_count:
        print("🎉 所有演示成功完成！程序功能正常。")
    else:
        print(f"⚠️ {total_count - success_count} 个演示失败，请检查相关问题。")
    print("🚀 A股板块数据分析程序演示结束")
    print(f"{'='*80}")
    
    return success_count == total_count

if __name__ == '__main__':
    main()
