import pandas as pd
import sys
import os

# 添加src到路径
sys.path.insert(0, 'src')

# 导入配置
import config

print("测试数据验证修复")
print("=" * 40)

# 1. 读取一个CSV文件
csv_file = 'data/20250722.csv'
print(f"1. 读取文件: {csv_file}")
df = pd.read_csv(csv_file)
print(f"   列名: {list(df.columns)}")
print(f"   形状: {df.shape}")

# 2. 检查必需列
required = config.DATA_VALIDATION['required_columns']
print(f"\n2. 必需列: {required}")
available = set(df.columns)
print(f"   可用列: {available}")
missing = set(required) - available
print(f"   缺少列: {missing}")

if not missing:
    print("   ✅ 单文件验证通过")
else:
    print("   ❌ 单文件验证失败")

# 3. 模拟设置多级索引
print(f"\n3. 设置多级索引...")
try:
    indexed_df = df.set_index(['date', 'sector_code'])
    print(f"   索引后形状: {indexed_df.shape}")
    print(f"   索引名称: {indexed_df.index.names}")
    print(f"   剩余列: {list(indexed_df.columns)}")
    
    # 4. 测试修复后的验证逻辑
    print(f"\n4. 测试修复后的验证逻辑...")
    available_columns = set(indexed_df.columns)
    if isinstance(indexed_df.index, pd.MultiIndex):
        available_columns.update(indexed_df.index.names)
    elif indexed_df.index.name:
        available_columns.add(indexed_df.index.name)
    
    print(f"   所有可用列: {available_columns}")
    missing_after_fix = set(required) - available_columns
    print(f"   修复后缺少列: {missing_after_fix}")
    
    if not missing_after_fix:
        print("   ✅ 修复后验证通过！")
    else:
        print("   ❌ 修复后仍然失败")
        
except Exception as e:
    print(f"   ❌ 设置索引失败: {e}")

print("\n测试完成")
