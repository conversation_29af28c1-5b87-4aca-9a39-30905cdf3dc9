#!/usr/bin/env python3
"""
增强分析功能演示脚本
展示HTML报告生成和历史分析功能
"""

import subprocess
import sys
import os
from pathlib import Path
import time

def demo_enhanced_features():
    """演示增强功能"""
    print("🚀 A股板块数据分析程序 - 增强功能演示")
    print("=" * 80)
    
    # 检查环境
    print("1. 环境检查...")
    if not Path('main.py').exists():
        print("❌ main.py 文件不存在")
        return False
    
    if not Path('data').exists():
        print("❌ data 目录不存在")
        return False
    
    csv_files = list(Path('data').glob('*.csv'))
    print(f"✅ 发现 {len(csv_files)} 个数据文件")
    
    if len(csv_files) < 100:
        print("⚠️ 数据文件较少，历史分析效果可能有限")
    
    # 演示用例
    demos = [
        {
            'name': '基础分析 + HTML报告',
            'description': '运行基础分析并生成HTML报告',
            'command': ['python', 'main.py', '--quick-mode', '--verbose', '--top-n', '8'],
            'timeout': 120
        },
        {
            'name': '历史分析 + HTML报告',
            'description': '启用历史同期分析功能',
            'command': ['python', 'main.py', '--quick-mode', '--enable-historical', '--verbose', '--top-n', '5'],
            'timeout': 180
        },
        {
            'name': '完整分析 + 数据导出',
            'description': '完整分析包含图表、CSV导出和HTML报告',
            'command': ['python', 'main.py', '--windows', '7,14', '--enable-historical', '--export-csv', '--top-n', '6'],
            'timeout': 240
        },
        {
            'name': '自定义时间窗口历史分析',
            'description': '自定义时间窗口的历史对比分析',
            'command': ['python', 'main.py', '--windows', '5,10,20', '--enable-historical', '--end-date', '2025-07-22', '--no-charts'],
            'timeout': 200
        }
    ]
    
    success_count = 0
    
    for i, demo in enumerate(demos, 1):
        print(f"\n{i}. {demo['name']}")
        print(f"   描述: {demo['description']}")
        print(f"   命令: {' '.join(demo['command'])}")
        print(f"   超时: {demo['timeout']} 秒")
        print("-" * 60)
        
        try:
            start_time = time.time()
            
            # 执行命令
            result = subprocess.run(
                demo['command'],
                capture_output=True,
                text=True,
                timeout=demo['timeout']
            )
            
            execution_time = time.time() - start_time
            
            if result.returncode == 0:
                print(f"✅ 执行成功")
                print(f"⏱️ 执行时间: {execution_time:.2f} 秒")
                success_count += 1
                
                # 检查输出文件
                check_output_files()
                
            else:
                print(f"❌ 执行失败，返回码: {result.returncode}")
                if result.stderr:
                    print(f"错误信息: {result.stderr[:200]}...")
            
        except subprocess.TimeoutExpired:
            print(f"⏰ 执行超时 ({demo['timeout']} 秒)")
        except Exception as e:
            print(f"❌ 执行异常: {str(e)}")
        
        print()
    
    # 演示总结
    print("=" * 80)
    print("演示总结")
    print("=" * 80)
    
    print(f"📊 执行统计:")
    print(f"   总演示数: {len(demos)}")
    print(f"   成功数量: {success_count}")
    print(f"   成功率: {success_count/len(demos)*100:.1f}%")
    
    # 检查生成的文件
    print(f"\n📁 生成的文件:")
    output_dir = Path('output')
    if output_dir.exists():
        files = list(output_dir.glob('*'))
        if files:
            print(f"   output目录: {len(files)} 个文件")
            
            # 按类型分类
            file_types = {}
            for file in files:
                ext = file.suffix.lower()
                file_types[ext] = file_types.get(ext, 0) + 1
            
            for ext, count in file_types.items():
                print(f"     {ext or '无扩展名'}: {count} 个")
            
            # 显示最新的HTML报告
            html_files = list(output_dir.glob('*.html'))
            if html_files:
                latest_html = max(html_files, key=lambda x: x.stat().st_mtime)
                print(f"\n📄 最新HTML报告: {latest_html.name}")
                print(f"   文件大小: {latest_html.stat().st_size / 1024:.1f} KB")
                print(f"   可以用浏览器打开查看详细分析结果")
        else:
            print("   output目录为空")
    else:
        print("   output目录不存在")
    
    # 功能特性说明
    print(f"\n✨ 增强功能特性:")
    print("   📊 HTML报告生成:")
    print("     • 专业的HTML格式分析报告")
    print("     • 包含所有分析结果和图表")
    print("     • 响应式设计，支持移动设备")
    print("     • 可分享和存档的完整报告")
    
    print("   📈 历史同期分析:")
    print("     • 跨年度同期时间窗口对比")
    print("     • 年度最佳/最差表现统计")
    print("     • 板块表现趋势分析")
    print("     • 一致性表现板块识别")
    
    print("   💾 数据导出增强:")
    print("     • CSV格式详细数据导出")
    print("     • JSON格式结构化数据")
    print("     • 高分辨率图表文件")
    print("     • 历史分析结果导出")
    
    print(f"\n💡 使用建议:")
    print("   • 首次使用建议先运行快速模式熟悉功能")
    print("   • 历史分析需要较多数据，建议有1年以上数据时启用")
    print("   • HTML报告包含完整分析结果，适合保存和分享")
    print("   • 可根据需要调整时间窗口和显示数量参数")
    
    return success_count == len(demos)

def check_output_files():
    """检查输出文件"""
    output_dir = Path('output')
    if output_dir.exists():
        files = list(output_dir.glob('*'))
        recent_files = sorted(files, key=lambda x: x.stat().st_mtime, reverse=True)[:3]
        
        if recent_files:
            print("   最新生成的文件:")
            for file in recent_files:
                size_kb = file.stat().st_size / 1024
                print(f"     - {file.name} ({size_kb:.1f} KB)")

def show_usage_examples():
    """显示使用示例"""
    print("\n📖 使用示例:")
    print("-" * 40)
    
    examples = [
        {
            'title': '基础分析',
            'command': 'python main.py',
            'description': '使用默认参数运行基础分析'
        },
        {
            'title': '启用历史分析',
            'command': 'python main.py --enable-historical',
            'description': '启用历史同期分析功能'
        },
        {
            'title': '快速模式 + 历史分析',
            'command': 'python main.py --quick-mode --enable-historical --verbose',
            'description': '快速模式下的历史分析'
        },
        {
            'title': '自定义时间窗口',
            'command': 'python main.py --windows 5,10,15,30 --enable-historical',
            'description': '自定义时间窗口的历史分析'
        },
        {
            'title': '完整分析 + 导出',
            'command': 'python main.py --enable-historical --export-csv --top-n 15',
            'description': '完整分析包含数据导出'
        }
    ]
    
    for example in examples:
        print(f"\n• {example['title']}:")
        print(f"  {example['command']}")
        print(f"  {example['description']}")

if __name__ == '__main__':
    print("🎯 A股板块数据分析程序 - 增强功能演示")
    
    # 显示使用示例
    show_usage_examples()
    
    # 询问是否运行演示
    print(f"\n❓ 是否运行完整演示？(y/n): ", end="")
    try:
        run_demo = input().lower().strip() == 'y'
        if run_demo:
            success = demo_enhanced_features()
            if success:
                print("\n🎉 所有演示成功完成！")
            else:
                print("\n😞 部分演示失败，请检查问题。")
        else:
            print("\n👋 演示已跳过。您可以直接使用上述命令运行程序。")
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
