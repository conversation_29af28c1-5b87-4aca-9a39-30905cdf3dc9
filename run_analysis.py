#!/usr/bin/env python3
"""
A股板块数据分析程序运行脚本
提供多种运行模式和参数组合示例
"""

import subprocess
import sys
import os
from pathlib import Path
import time

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    print("-" * 50)
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查必要文件
    required_files = ['main.py', 'config.py', 'requirements.txt']
    for file in required_files:
        if Path(file).exists():
            print(f"✅ {file} 存在")
        else:
            print(f"❌ {file} 不存在")
            return False
    
    # 检查src目录
    if Path('src').exists():
        src_files = ['data_loader.py', 'time_window_analyzer.py', 'ranking_analyzer.py', 
                    'visualizer.py', 'report_generator.py', 'utils.py']
        missing_files = []
        for file in src_files:
            if not Path(f'src/{file}').exists():
                missing_files.append(file)
        
        if missing_files:
            print(f"❌ src目录缺少文件: {missing_files}")
            return False
        else:
            print(f"✅ src目录完整 ({len(src_files)}个模块)")
    else:
        print("❌ src目录不存在")
        return False
    
    # 检查数据目录
    if Path('data').exists():
        csv_files = list(Path('data').glob('*.csv'))
        print(f"✅ data目录存在，包含 {len(csv_files)} 个CSV文件")
        if len(csv_files) < 5:
            print("⚠️ 数据文件较少，建议至少有5个文件")
    else:
        print("❌ data目录不存在，请确保有数据文件")
        return False
    
    print("\n✅ 环境检查通过！")
    return True

def install_dependencies():
    """安装依赖包"""
    print("\n📦 安装依赖包...")
    print("-" * 50)
    
    try:
        # 尝试安装依赖
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ 依赖包安装成功")
            return True
        else:
            print(f"❌ 依赖包安装失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ 依赖包安装超时")
        return False
    except Exception as e:
        print(f"❌ 依赖包安装异常: {str(e)}")
        return False

def run_program(mode="basic"):
    """运行程序"""
    print(f"\n🚀 运行程序 - {mode}模式")
    print("-" * 50)
    
    # 不同运行模式的参数配置
    run_configs = {
        "basic": {
            "args": ["--verbose"],
            "description": "基础模式：使用默认参数运行完整分析",
            "timeout": 300
        },
        "quick": {
            "args": ["--quick-mode", "--verbose", "--top-n", "5"],
            "description": "快速模式：使用最近50个文件，显示前5名",
            "timeout": 180
        },
        "custom": {
            "args": ["--windows", "7,14", "--top-n", "8", "--export-csv", "--verbose"],
            "description": "自定义模式：7日和14日窗口，前8名，导出CSV",
            "timeout": 240
        },
        "no-charts": {
            "args": ["--quick-mode", "--no-charts", "--verbose"],
            "description": "无图表模式：快速分析，不生成图表",
            "timeout": 120
        },
        "limited": {
            "args": ["--max-files", "30", "--windows", "7", "--verbose"],
            "description": "限制模式：只使用最近30个文件，7日窗口",
            "timeout": 150
        }
    }
    
    if mode not in run_configs:
        print(f"❌ 未知运行模式: {mode}")
        return False
    
    config = run_configs[mode]
    print(f"📋 {config['description']}")
    print(f"🔧 参数: {' '.join(config['args'])}")
    print(f"⏱️ 超时: {config['timeout']} 秒")
    print()
    
    try:
        start_time = time.time()
        
        # 执行程序
        cmd = [sys.executable, 'main.py'] + config['args']
        result = subprocess.run(cmd, text=True, timeout=config['timeout'])
        
        execution_time = time.time() - start_time
        
        if result.returncode == 0:
            print(f"\n✅ 程序执行成功")
            print(f"⏱️ 执行时间: {execution_time:.2f} 秒")
            
            # 检查输出文件
            check_output_files()
            return True
        else:
            print(f"\n❌ 程序执行失败，返回码: {result.returncode}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"\n⏰ 程序执行超时 ({config['timeout']} 秒)")
        return False
    except Exception as e:
        print(f"\n❌ 程序执行异常: {str(e)}")
        return False

def check_output_files():
    """检查输出文件"""
    print("\n📁 检查输出文件...")
    print("-" * 30)
    
    output_dir = Path('output')
    if output_dir.exists():
        files = list(output_dir.glob('*'))
        if files:
            total_size = sum(f.stat().st_size for f in files) / 1024 / 1024
            print(f"📊 输出文件: {len(files)} 个")
            print(f"💾 总大小: {total_size:.2f} MB")
            
            # 按类型分类
            file_types = {}
            for file in files:
                ext = file.suffix.lower()
                file_types[ext] = file_types.get(ext, 0) + 1
            
            print(f"📋 文件类型: {dict(file_types)}")
            
            # 显示最新的3个文件
            recent_files = sorted(files, key=lambda x: x.stat().st_mtime, reverse=True)[:3]
            print("📄 最新文件:")
            for file in recent_files:
                size_kb = file.stat().st_size / 1024
                print(f"   - {file.name} ({size_kb:.1f} KB)")
        else:
            print("📂 输出目录为空")
    else:
        print("📂 输出目录不存在")

def main():
    """主函数"""
    print("🎯 A股板块数据分析程序 - 运行助手")
    print("=" * 80)
    
    # 1. 环境检查
    if not check_environment():
        print("\n❌ 环境检查失败，请解决问题后重试")
        return False
    
    # 2. 安装依赖（可选）
    print("\n❓ 是否需要安装/更新依赖包？(y/n): ", end="")
    try:
        install_deps = input().lower().strip() == 'y'
        if install_deps:
            if not install_dependencies():
                print("\n⚠️ 依赖包安装失败，但可以尝试继续运行")
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
        return False
    
    # 3. 选择运行模式
    print("\n🎮 选择运行模式:")
    print("1. basic   - 基础模式（完整分析，推荐）")
    print("2. quick   - 快速模式（适合测试）")
    print("3. custom  - 自定义模式（7日+14日窗口）")
    print("4. no-charts - 无图表模式（纯文本输出）")
    print("5. limited - 限制模式（少量数据）")
    print("\n请选择模式 (1-5) 或直接按回车使用基础模式: ", end="")
    
    try:
        choice = input().strip()
        mode_map = {
            '1': 'basic', '2': 'quick', '3': 'custom', 
            '4': 'no-charts', '5': 'limited', '': 'basic'
        }
        
        mode = mode_map.get(choice, 'basic')
        
        # 4. 运行程序
        success = run_program(mode)
        
        if success:
            print("\n🎉 分析完成！请查看output目录中的结果文件。")
            print("\n💡 提示：")
            print("   - CSV文件包含详细数据")
            print("   - PNG文件是可视化图表")
            print("   - HTML文件是综合报告")
        else:
            print("\n😞 分析失败，请检查错误信息。")
        
        return success
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
        return False

if __name__ == '__main__':
    main()
