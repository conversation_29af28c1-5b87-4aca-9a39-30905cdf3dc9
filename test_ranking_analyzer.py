#!/usr/bin/env python3
"""
RankingAnalyzer模块测试脚本
"""

import sys
import os
sys.path.append('src')

from data_loader import DataLoader
from ranking_analyzer import RankingAnalyzer
import config
import pandas as pd

def test_ranking_analyzer():
    """测试RankingAnalyzer功能"""
    print("=" * 80)
    print("RankingAnalyzer模块测试")
    print("=" * 80)
    
    try:
        # 1. 加载测试数据
        print("1. 加载测试数据...")
        loader = DataLoader()
        # 只加载前30个文件进行测试
        loader.file_list = loader.file_list[:30]
        data = loader.load_all_data()
        print(f"   ✓ 数据加载完成，形状: {data.shape}")
        
        # 2. 初始化RankingAnalyzer
        print("\n2. 初始化RankingAnalyzer...")
        analyzer = RankingAnalyzer(data)
        print(f"   ✓ 分析器初始化完成")
        print(f"   ✓ 数据日期范围: {analyzer.available_dates[0]} 到 {analyzer.available_dates[-1]}")
        print(f"   ✓ 总交易日数: {len(analyzer.available_dates)}")
        print(f"   ✓ 板块总数: {len(analyzer.all_sectors)}")
        
        # 3. 测试每日排名计算
        print("\n3. 测试每日排名计算...")
        daily_rankings = analyzer.calculate_daily_rankings()
        print(f"   ✓ 每日排名计算完成，共 {len(daily_rankings)} 条记录")
        
        # 显示某一天的排名示例
        if not daily_rankings.empty:
            sample_date = analyzer.available_dates[-1]
            sample_day_rankings = daily_rankings.loc[sample_date].sort_values('rank')
            print(f"\n   {sample_date} 排名前5:")
            for i, (sector_code, row) in enumerate(sample_day_rankings.head(5).iterrows()):
                print(f"     {row['rank']:2d}. {row['sector_name']} ({sector_code}): {row['change_pct']:.2f}%")
        
        # 4. 测试单日冠军统计
        print("\n4. 测试单日冠军统计...")
        champions = analyzer.count_daily_champions()
        print(f"   ✓ 单日冠军统计完成，共 {len(champions)} 个板块获得过冠军")
        
        if not champions.empty:
            print("\n   冠军次数前5名:")
            for idx, row in champions.head(5).iterrows():
                print(f"     {row['rank']}. {row['sector_name']} ({row['sector_code']}): "
                      f"{row['champion_count']}次 ({row['champion_frequency_pct']:.2f}%)")
        
        # 5. 测试前10频次统计
        print("\n5. 测试前10频次统计...")
        top10_freq = analyzer.count_top10_frequency(window_days=20)
        print(f"   ✓ 前10频次统计完成，共 {len(top10_freq)} 个板块进入过前10")
        
        if not top10_freq.empty:
            print("\n   前10频次前5名:")
            for idx, row in top10_freq.head(5).iterrows():
                print(f"     {row['rank']}. {row['sector_name']} ({row['sector_code']}): "
                      f"{row['top10_count']}次 ({row['top10_frequency_pct']:.2f}%)")
        
        # 6. 测试排名统计报告生成
        print("\n6. 测试排名统计报告生成...")
        report = analyzer.generate_ranking_report(window_days=20)
        
        if 'error' not in report:
            print("   ✓ 报告生成成功")
            print(f"     分析交易日数: {report['analysis_info']['analysis_window_days']}")
            print(f"     冠军板块总数: {report['daily_champions']['summary']['total_champions']}")
            print(f"     进入前10板块数: {report['top10_frequency']['summary']['sectors_in_top10']}")
            
            if report['daily_champions']['summary']['most_champion_sector']:
                print(f"     最多冠军板块: {report['daily_champions']['summary']['most_champion_sector']} "
                      f"({report['daily_champions']['summary']['most_champion_count']}次)")
        else:
            print(f"   ✗ 报告生成失败: {report['error']}")
        
        # 7. 测试板块排名历史查询
        print("\n7. 测试板块排名历史查询...")
        if not champions.empty:
            sample_sector = champions.iloc[0]['sector_code']
            sector_history = analyzer.get_sector_ranking_history(sample_sector, limit=10)
            print(f"   ✓ 获取板块 {sample_sector} 的排名历史，共 {len(sector_history)} 条记录")
            
            if not sector_history.empty:
                print(f"   最近5次排名:")
                for idx, row in sector_history.head(5).iterrows():
                    print(f"     {row['date'].strftime('%Y-%m-%d')}: 第{row['rank']}名 ({row['change_pct']:.2f}%)")
        
        # 8. 测试连续表现优秀板块查找
        print("\n8. 测试连续表现优秀板块查找...")
        consecutive = analyzer.find_consecutive_top_performers(min_days=2, rank_threshold=5)
        print(f"   ✓ 找到 {len(consecutive)} 个连续表现优秀的记录")
        
        if not consecutive.empty:
            print("   连续表现优秀的前3个记录:")
            for idx, row in consecutive.head(3).iterrows():
                print(f"     {row['sector_name']}: {row['start_date'].strftime('%m-%d')} 到 "
                      f"{row['end_date'].strftime('%m-%d')} (连续{row['consecutive_days']}天)")
        
        # 9. 测试排名摘要
        print("\n9. 测试排名摘要...")
        summary = analyzer.get_ranking_summary()
        
        if 'error' not in summary:
            print("   ✓ 排名摘要生成成功")
            print(f"     总记录数: {summary['data_overview']['total_records']:,}")
            print(f"     平均排名: {summary['ranking_stats']['avg_rank']:.1f}")
            print(f"     平均涨跌幅: {summary['ranking_stats']['avg_change_pct']:.2f}%")
            print(f"     冠军记录数: {summary['top_performance_stats']['total_champion_records']}")
        
        # 10. 测试缓存功能
        print("\n10. 测试缓存功能...")
        cache_info = analyzer.get_cache_info()
        print(f"   ✓ 缓存状态: {'已缓存' if cache_info['daily_rankings_cached'] else '未缓存'}")
        print(f"   ✓ 缓存大小: {cache_info['cache_size']} 条记录")
        
        # 清除缓存
        analyzer.clear_cache()
        cache_info_after = analyzer.get_cache_info()
        print(f"   ✓ 清除后缓存状态: {'已缓存' if cache_info_after['daily_rankings_cached'] else '未缓存'}")
        
        print("\n" + "=" * 80)
        print("测试完成！RankingAnalyzer模块工作正常。")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        print(f"\n测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    test_ranking_analyzer()
