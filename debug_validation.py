#!/usr/bin/env python3
"""
调试数据验证问题
"""

import sys
import os
sys.path.append('src')

import pandas as pd
import config
from pathlib import Path

def debug_data_structure():
    """调试数据结构"""
    print("🔍 调试数据验证问题")
    print("=" * 60)
    
    # 1. 检查配置
    print("1. 检查配置:")
    print(f"   required_columns: {config.DATA_VALIDATION['required_columns']}")
    
    # 2. 检查单个CSV文件
    print("\n2. 检查单个CSV文件:")
    data_dir = Path(config.DATA_DIR)
    csv_files = list(data_dir.glob("*.csv"))
    
    if csv_files:
        sample_file = csv_files[0]
        print(f"   样本文件: {sample_file.name}")
        
        # 读取文件
        df = pd.read_csv(sample_file)
        print(f"   原始列名: {list(df.columns)}")
        print(f"   数据形状: {df.shape}")
        print(f"   前3行:")
        print(df.head(3))
        
        # 检查必需列
        required = set(config.DATA_VALIDATION['required_columns'])
        available = set(df.columns)
        missing = required - available
        
        print(f"\n   必需列: {required}")
        print(f"   可用列: {available}")
        print(f"   缺少列: {missing}")
        
        if not missing:
            print("   ✅ 单文件包含所有必需列")
        else:
            print(f"   ❌ 单文件缺少列: {missing}")
    
    # 3. 模拟数据加载过程
    print("\n3. 模拟数据加载过程:")
    
    # 读取几个文件并合并
    dfs = []
    for i, file in enumerate(csv_files[:3]):  # 只用前3个文件
        df = pd.read_csv(file)
        print(f"   文件 {i+1}: {file.name}, 形状: {df.shape}")
        dfs.append(df)
    
    # 合并数据
    combined = pd.concat(dfs, ignore_index=True)
    print(f"\n   合并后数据形状: {combined.shape}")
    print(f"   合并后列名: {list(combined.columns)}")
    
    # 4. 设置多级索引（这是关键步骤）
    print("\n4. 设置多级索引:")
    try:
        indexed_data = combined.set_index(['date', 'sector_code']).sort_index()
        print(f"   索引后数据形状: {indexed_data.shape}")
        print(f"   索引类型: {type(indexed_data.index)}")
        print(f"   索引名称: {indexed_data.index.names}")
        print(f"   剩余列名: {list(indexed_data.columns)}")
        
        # 5. 测试验证逻辑
        print("\n5. 测试验证逻辑:")
        required_columns = config.DATA_VALIDATION['required_columns']
        print(f"   必需列: {required_columns}")
        
        # 获取所有可用的列名（包括索引）
        available_columns = set(indexed_data.columns)
        print(f"   DataFrame列: {available_columns}")
        
        if isinstance(indexed_data.index, pd.MultiIndex):
            print(f"   多级索引名称: {indexed_data.index.names}")
            available_columns.update(indexed_data.index.names)
        elif indexed_data.index.name:
            print(f"   单级索引名称: {indexed_data.index.name}")
            available_columns.add(indexed_data.index.name)
        
        print(f"   所有可用列（含索引）: {available_columns}")
        
        missing_columns = set(required_columns) - available_columns
        print(f"   缺少列: {missing_columns}")
        
        if missing_columns:
            print("   ❌ 验证失败")
        else:
            print("   ✅ 验证通过")
            
    except Exception as e:
        print(f"   ❌ 设置索引失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    debug_data_structure()
