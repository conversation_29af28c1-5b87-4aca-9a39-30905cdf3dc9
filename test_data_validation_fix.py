#!/usr/bin/env python3
"""
测试数据验证修复
"""

import sys
import os
sys.path.append('src')

from data_loader import DataLoader
import config
import pandas as pd

def test_data_validation_fix():
    """测试数据验证修复"""
    print("=" * 80)
    print("测试数据验证修复")
    print("=" * 80)
    
    try:
        # 1. 初始化DataLoader
        print("1. 初始化DataLoader...")
        loader = DataLoader()
        print(f"   ✓ 找到 {len(loader.file_list)} 个数据文件")
        
        # 2. 测试单个文件加载
        print("\n2. 测试单个文件加载...")
        test_file = loader.file_list[0]
        print(f"   测试文件: {test_file.name}")
        
        single_df = loader._load_single_file(test_file)
        if single_df is not None:
            print(f"   ✓ 单文件加载成功")
            print(f"   ✓ 数据形状: {single_df.shape}")
            print(f"   ✓ 列名: {list(single_df.columns)}")
            
            # 检查是否包含必需列
            required_columns = config.DATA_VALIDATION['required_columns']
            missing_in_single = set(required_columns) - set(single_df.columns)
            if missing_in_single:
                print(f"   ⚠️ 单文件缺少列: {missing_in_single}")
            else:
                print(f"   ✅ 单文件包含所有必需列")
        else:
            print("   ❌ 单文件加载失败")
            return False
        
        # 3. 测试少量文件的完整加载流程
        print("\n3. 测试少量文件的完整加载流程...")
        # 只使用前5个文件进行测试
        original_files = loader.file_list.copy()
        loader.file_list = loader.file_list[:5]
        
        print(f"   使用 {len(loader.file_list)} 个文件进行测试")
        
        # 加载数据
        data = loader.load_all_data()
        print(f"   ✓ 数据加载完成，形状: {data.shape}")
        print(f"   ✓ 索引类型: {type(data.index)}")
        print(f"   ✓ 索引名称: {data.index.names}")
        print(f"   ✓ 列名: {list(data.columns)}")
        
        # 4. 测试数据验证
        print("\n4. 测试数据验证...")
        is_valid = loader.validate_data(data)
        
        if is_valid:
            print("   ✅ 数据验证通过！")
        else:
            print("   ❌ 数据验证失败")
            return False
        
        # 5. 显示验证详情
        print("\n5. 验证详情:")
        required_columns = config.DATA_VALIDATION['required_columns']
        print(f"   必需列: {required_columns}")
        
        # 获取所有可用的列名（包括索引）
        available_columns = set(data.columns)
        if isinstance(data.index, pd.MultiIndex):
            available_columns.update(data.index.names)
        elif data.index.name:
            available_columns.add(data.index.name)
        
        print(f"   可用列: {available_columns}")
        
        missing_columns = set(required_columns) - available_columns
        if missing_columns:
            print(f"   ❌ 缺少列: {missing_columns}")
        else:
            print(f"   ✅ 所有必需列都存在")
        
        # 6. 显示数据样本
        print("\n6. 数据样本:")
        print("   前5行数据:")
        sample = data.head()
        print(f"   索引: {sample.index.tolist()}")
        print(f"   列: {list(sample.columns)}")
        if 'change_pct' in sample.columns:
            print(f"   change_pct样本: {sample['change_pct'].tolist()}")
        
        # 恢复原始文件列表
        loader.file_list = original_files
        
        print("\n" + "=" * 80)
        print("✅ 数据验证修复测试成功！")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_config_validation():
    """测试配置验证"""
    print("\n" + "=" * 80)
    print("测试配置验证")
    print("=" * 80)
    
    print("配置信息:")
    print(f"  DATA_DIR: {config.DATA_DIR}")
    print(f"  required_columns: {config.DATA_VALIDATION['required_columns']}")
    
    # 检查数据目录
    from pathlib import Path
    data_dir = Path(config.DATA_DIR)
    if data_dir.exists():
        csv_files = list(data_dir.glob("*.csv"))
        print(f"  ✅ 数据目录存在，包含 {len(csv_files)} 个CSV文件")
        
        if csv_files:
            # 检查第一个文件的列名
            import pandas as pd
            sample_file = csv_files[0]
            try:
                sample_df = pd.read_csv(sample_file, nrows=1)
                print(f"  ✅ 样本文件列名: {list(sample_df.columns)}")
                
                # 检查列名匹配
                required = set(config.DATA_VALIDATION['required_columns'])
                available = set(sample_df.columns)
                missing = required - available
                
                if missing:
                    print(f"  ⚠️ 样本文件缺少列: {missing}")
                else:
                    print(f"  ✅ 样本文件包含所有必需列")
                    
            except Exception as e:
                print(f"  ❌ 读取样本文件失败: {str(e)}")
    else:
        print(f"  ❌ 数据目录不存在: {data_dir}")

if __name__ == '__main__':
    print("🔍 数据验证修复测试")
    
    # 测试配置
    test_config_validation()
    
    # 测试数据验证修复
    success = test_data_validation_fix()
    
    if success:
        print("\n🎉 所有测试通过！现在可以运行主程序了。")
        print("\n💡 建议运行命令:")
        print("   python main.py --quick-mode --verbose")
    else:
        print("\n😞 测试失败，请检查问题。")
