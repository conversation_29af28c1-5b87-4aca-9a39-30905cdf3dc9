#!/usr/bin/env python3
"""
RankingAnalyzer模块演示脚本
展示排名统计分析功能的完整使用
"""

import sys
import os
sys.path.append('src')

from data_loader import DataLoader
from ranking_analyzer import RankingAnalyzer
import config
import pandas as pd

def main():
    """主演示函数"""
    print("=" * 90)
    print("A股板块数据分析程序 - RankingAnalyzer模块演示")
    print("=" * 90)
    
    try:
        # 1. 加载数据
        print("\n1. 加载演示数据...")
        loader = DataLoader()
        # 加载前50个文件进行演示
        loader.file_list = loader.file_list[:50]
        data = loader.load_all_data()
        print(f"   ✓ 数据加载完成，形状: {data.shape}")
        
        # 2. 初始化分析器
        print("\n2. 初始化排名分析器...")
        analyzer = RankingAnalyzer(data)
        print(f"   ✓ 分析器初始化完成")
        print(f"   ✓ 数据日期范围: {analyzer.available_dates[0]} 到 {analyzer.available_dates[-1]}")
        print(f"   ✓ 可用交易日: {len(analyzer.available_dates)} 天")
        print(f"   ✓ 板块总数: {len(analyzer.all_sectors)} 个")
        
        # 3. 每日排名分析演示
        print("\n3. 每日排名分析演示...")
        daily_rankings = analyzer.calculate_daily_rankings()
        print(f"   ✓ 每日排名计算完成，共 {len(daily_rankings)} 条记录")
        
        # 显示最新一天的排名
        latest_date = analyzer.available_dates[-1]
        latest_rankings = daily_rankings.loc[latest_date].sort_values('rank')
        
        print(f"\n   📊 {latest_date.strftime('%Y-%m-%d')} 板块涨跌幅排行榜 (前10名):")
        print("   " + "-" * 75)
        print("   排名  板块名称           板块代码    涨跌幅     是否冠军")
        print("   " + "-" * 75)
        
        for sector_code, row in latest_rankings.head(10).iterrows():
            champion_mark = "★" if row['is_champion'] else " "
            print(f"   {row['rank']:2d}    {row['sector_name']:<12} {sector_code:<8} "
                  f"{row['change_pct']:7.2f}%    {champion_mark}")
        print("   " + "-" * 75)
        
        # 4. 单日冠军统计演示
        print(f"\n4. 单日冠军统计分析...")
        champions = analyzer.count_daily_champions()
        print(f"   ✓ 单日冠军统计完成，共 {len(champions)} 个板块获得过冠军")
        
        print(f"\n   🏆 单日冠军次数排行榜 (前10名):")
        print("   " + "-" * 85)
        print("   排名  板块名称           冠军次数  冠军频率   平均冠军涨跌幅  最大冠军涨跌幅")
        print("   " + "-" * 85)
        
        for idx, row in champions.head(10).iterrows():
            print(f"   {row['rank']:2d}    {row['sector_name']:<12} {row['champion_count']:4d}次   "
                  f"{row['champion_frequency_pct']:6.2f}%   {row['avg_champion_change_pct']:8.2f}%   "
                  f"{row['max_champion_change_pct']:8.2f}%")
        print("   " + "-" * 85)
        
        # 5. 前10频次统计演示
        print(f"\n5. 前10频次统计分析...")
        window_days = 30
        top10_freq = analyzer.count_top10_frequency(window_days=window_days)
        print(f"   ✓ 前10频次统计完成 (最近{window_days}个交易日)，共 {len(top10_freq)} 个板块进入过前10")
        
        print(f"\n   📈 前10频次排行榜 (前10名):")
        print("   " + "-" * 95)
        print("   排名  板块名称           前10次数  前10频率  前5次数  前3次数  冠军次数  平均排名")
        print("   " + "-" * 95)
        
        for idx, row in top10_freq.head(10).iterrows():
            print(f"   {row['rank']:2d}    {row['sector_name']:<12} {row['top10_count']:4d}次   "
                  f"{row['top10_frequency_pct']:6.2f}%  {row['top5_count']:3d}次   "
                  f"{row['top3_count']:3d}次   {row['champion_count']:3d}次   {row['avg_rank_in_top10']:6.1f}")
        print("   " + "-" * 95)
        
        # 6. 连续表现优秀板块分析
        print(f"\n6. 连续表现优秀板块分析...")
        consecutive = analyzer.find_consecutive_top_performers(min_days=3, rank_threshold=5)
        print(f"   ✓ 找到 {len(consecutive)} 个连续3天以上排名前5的记录")
        
        if not consecutive.empty:
            print(f"\n   🔥 连续表现优秀记录 (前8名):")
            print("   " + "-" * 85)
            print("   板块名称           开始日期    结束日期    连续天数  平均排名  最佳排名")
            print("   " + "-" * 85)
            
            for idx, row in consecutive.head(8).iterrows():
                print(f"   {row['sector_name']:<12} {row['start_date'].strftime('%Y-%m-%d')}  "
                      f"{row['end_date'].strftime('%Y-%m-%d')}  {row['consecutive_days']:4d}天   "
                      f"{row['avg_rank']:6.1f}   {row['best_rank']:4.0f}")
            print("   " + "-" * 85)
        
        # 7. 特定板块排名历史分析
        print(f"\n7. 特定板块排名历史分析...")
        if not champions.empty:
            sample_sector = champions.iloc[0]['sector_code']
            sample_name = champions.iloc[0]['sector_name']
            
            print(f"   分析板块: {sample_name} ({sample_sector})")
            
            sector_history = analyzer.get_sector_ranking_history(sample_sector, limit=15)
            
            if not sector_history.empty:
                print(f"\n   📅 {sample_name} 最近15次排名历史:")
                print("   " + "-" * 65)
                print("   日期        排名   涨跌幅    是否冠军  是否前10  是否前5")
                print("   " + "-" * 65)
                
                for idx, row in sector_history.iterrows():
                    champion_mark = "★" if row['is_champion'] else " "
                    top10_mark = "●" if row['is_top10'] else " "
                    top5_mark = "◆" if row['is_top5'] else " "
                    
                    print(f"   {row['date'].strftime('%Y-%m-%d')}  {row['rank']:3d}   "
                          f"{row['change_pct']:6.2f}%   {champion_mark:4s}   {top10_mark:4s}   {top5_mark}")
                print("   " + "-" * 65)
        
        # 8. 综合排名统计报告
        print(f"\n8. 综合排名统计报告...")
        report = analyzer.generate_ranking_report(window_days=30)
        
        if 'error' not in report:
            print("   ✓ 综合报告生成成功")
            
            analysis_info = report['analysis_info']
            champions_summary = report['daily_champions']['summary']
            top10_summary = report['top10_frequency']['summary']
            
            print(f"\n   📋 分析摘要:")
            print(f"   ├─ 分析时间窗口: 最近 {analysis_info['analysis_window_days']} 个交易日")
            print(f"   ├─ 总板块数: {analysis_info['total_sectors']} 个")
            print(f"   ├─ 获得过冠军的板块: {champions_summary['total_champions']} 个")
            print(f"   ├─ 最多冠军板块: {champions_summary['most_champion_sector']} "
                  f"({champions_summary['most_champion_count']}次)")
            print(f"   ├─ 进入过前10的板块: {top10_summary['sectors_in_top10']} 个")
            print(f"   ├─ 最频繁前10板块: {top10_summary['most_frequent_sector']} "
                  f"({top10_summary['most_frequent_count']}次)")
            print(f"   └─ 平均每板块冠军次数: {champions_summary['avg_champions_per_sector']:.1f} 次")
        
        # 9. 排名分布分析
        print(f"\n9. 排名分布分析...")
        summary = analyzer.get_ranking_summary()
        
        if 'error' not in summary:
            ranking_stats = summary['ranking_stats']
            top_stats = summary['top_performance_stats']
            
            print(f"   📊 排名统计概览:")
            print(f"   ├─ 平均排名: {ranking_stats['avg_rank']:.1f}")
            print(f"   ├─ 中位数排名: {ranking_stats['median_rank']:.1f}")
            print(f"   ├─ 平均涨跌幅: {ranking_stats['avg_change_pct']:.2f}%")
            print(f"   ├─ 中位数涨跌幅: {ranking_stats['median_change_pct']:.2f}%")
            print(f"   ├─ 冠军记录总数: {top_stats['total_champion_records']:,} 条")
            print(f"   ├─ 前10记录总数: {top_stats['total_top10_records']:,} 条")
            print(f"   ├─ 前5记录总数: {top_stats['total_top5_records']:,} 条")
            print(f"   └─ 前3记录总数: {top_stats['total_top3_records']:,} 条")
        
        print("\n" + "=" * 90)
        print("演示完成！RankingAnalyzer模块功能展示结束。")
        print("\n核心功能总结:")
        print("  ✓ 每日板块涨跌幅排名计算")
        print("  ✓ 单日冠军板块统计分析")
        print("  ✓ 前10名频次统计分析")
        print("  ✓ 连续表现优秀板块识别")
        print("  ✓ 特定板块排名历史查询")
        print("  ✓ 综合排名统计报告生成")
        print("  ✓ 排名分布和趋势分析")
        print("  ✓ 并列排名处理")
        print("  ✓ 多维度统计指标")
        print("  ✓ 灵活的时间窗口分析")
        print("=" * 90)
        
    except Exception as e:
        print(f"\n演示过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
