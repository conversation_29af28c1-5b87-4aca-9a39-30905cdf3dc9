#!/usr/bin/env python3
"""
DataLoader模块测试脚本
"""

import sys
import os
sys.path.append('src')

from data_loader import DataLoader
import config

def test_data_loader():
    """测试DataLoader功能"""
    print("=" * 60)
    print("DataLoader模块测试")
    print("=" * 60)
    
    try:
        # 初始化DataLoader
        print("1. 初始化DataLoader...")
        loader = DataLoader()
        print(f"   找到 {len(loader.file_list)} 个数据文件")
        
        # 获取日期范围
        print("\n2. 获取数据日期范围...")
        date_range = loader.get_date_range()
        print(f"   日期范围: {date_range[0]} 到 {date_range[1]}")
        
        # 加载少量数据进行测试（只加载前10个文件）
        print("\n3. 加载测试数据（前10个文件）...")
        original_file_list = loader.file_list
        loader.file_list = loader.file_list[:10]  # 只测试前10个文件
        
        data = loader.load_all_data()
        print(f"   加载完成，数据形状: {data.shape}")
        print(f"   数据列: {list(data.columns)}")
        
        # 验证数据
        print("\n4. 验证数据完整性...")
        is_valid = loader.validate_data(data)
        print(f"   验证结果: {'通过' if is_valid else '失败'}")
        
        # 获取数据摘要
        print("\n5. 获取数据摘要...")
        summary = loader.get_data_summary()
        print(f"   总记录数: {summary['total_records']:,}")
        print(f"   板块数量: {summary['total_sectors']}")
        print(f"   内存使用: {summary['memory_usage_mb']:.2f} MB")
        
        # 获取板块列表
        print("\n6. 获取板块列表...")
        sectors = loader.get_sector_list()
        print(f"   前10个板块: {sectors[:10]}")
        
        # 显示数据样本
        print("\n7. 数据样本:")
        print(data.head())
        
        print("\n" + "=" * 60)
        print("测试完成！DataLoader模块工作正常。")
        print("=" * 60)
        
        # 恢复原始文件列表
        loader.file_list = original_file_list
        
        return True
        
    except Exception as e:
        print(f"\n测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    test_data_loader()
