#!/usr/bin/env python3
"""
DataLoader模块演示脚本
展示数据加载功能的基本使用
"""

import sys
import os
sys.path.append('src')

from data_loader import DataLoader
import config

def main():
    """主演示函数"""
    print("=" * 80)
    print("A股板块数据分析程序 - DataLoader模块演示")
    print("=" * 80)
    
    try:
        # 1. 初始化DataLoader
        print("\n1. 初始化DataLoader...")
        loader = DataLoader()
        print(f"   ✓ 成功初始化，找到 {len(loader.file_list)} 个数据文件")
        
        # 2. 获取基本信息
        print("\n2. 获取数据基本信息...")
        date_range = loader.get_date_range()
        print(f"   ✓ 数据日期范围: {date_range[0]} 到 {date_range[1]}")
        
        # 3. 加载部分数据进行演示（前20个文件）
        print("\n3. 加载演示数据（前20个文件）...")
        print("   注意：为了演示目的，只加载前20个文件以节省时间和内存")
        
        # 备份原始文件列表
        original_files = loader.file_list.copy()
        loader.file_list = loader.file_list[:20]
        
        # 加载数据
        data = loader.load_all_data()
        print(f"   ✓ 数据加载完成")
        print(f"     - 数据形状: {data.shape}")
        print(f"     - 索引类型: {type(data.index)}")
        print(f"     - 索引名称: {data.index.names}")
        
        # 4. 数据验证
        print("\n4. 数据验证...")
        is_valid = loader.validate_data(data)
        status = "✓ 通过" if is_valid else "✗ 失败"
        print(f"   {status}")
        
        # 5. 获取数据摘要
        print("\n5. 数据摘要...")
        summary = loader.get_data_summary()
        print(f"   ✓ 总记录数: {summary['total_records']:,}")
        print(f"   ✓ 板块数量: {summary['total_sectors']}")
        print(f"   ✓ 处理文件数: {summary['total_files']}")
        print(f"   ✓ 内存使用: {summary['memory_usage_mb']:.2f} MB")
        
        # 6. 显示板块信息
        print("\n6. 板块信息...")
        sectors = loader.get_sector_list()
        print(f"   ✓ 总板块数: {len(sectors)}")
        print(f"   ✓ 前10个板块: {sectors[:10]}")
        
        # 7. 显示数据样本
        print("\n7. 数据样本（前5行）:")
        print("-" * 80)
        sample_data = data.head()
        print(sample_data[['sector_name', 'close', 'change_pct', 'volume']])
        print("-" * 80)
        
        # 8. 显示数据统计信息
        print("\n8. 关键字段统计信息:")
        print("-" * 80)
        stats = data[['change_pct', 'close', 'volume']].describe()
        print(stats)
        print("-" * 80)
        
        # 恢复原始文件列表
        loader.file_list = original_files
        
        print("\n" + "=" * 80)
        print("演示完成！DataLoader模块功能正常。")
        print("\n主要功能:")
        print("  ✓ 批量加载CSV文件")
        print("  ✓ 数据类型优化")
        print("  ✓ 内存使用控制")
        print("  ✓ 数据完整性验证")
        print("  ✓ 多级索引设置")
        print("  ✓ 进度显示")
        print("  ✓ 错误处理")
        print("=" * 80)
        
    except Exception as e:
        print(f"\n演示过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
