#!/usr/bin/env python3
"""
测试基本HTML报告生成功能
"""

import sys
import os
from pathlib import Path
import pandas as pd

# 添加src到路径
sys.path.insert(0, 'src')

def test_basic_html_report():
    """测试基本HTML报告生成"""
    print("🔍 测试基本HTML报告生成")
    print("=" * 60)
    
    try:
        from report_generator import ReportGenerator
        
        # 1. 创建报告生成器
        print("1. 创建报告生成器...")
        output_dir = 'test_html_output'
        report_generator = ReportGenerator(output_dir)
        print(f"   ✅ 报告生成器创建成功，输出目录: {output_dir}")
        
        # 2. 创建测试数据
        print("\n2. 创建测试数据...")
        
        # 时间窗口分析数据
        window_performance = {
            7: pd.DataFrame({
                'sector_name': ['工程机械', '水泥建材', '钢铁行业', '有色金属', '煤炭行业'],
                'sector_code': ['BK0437', 'BK0425', 'BK0427', 'BK0478', 'BK0437'],
                'cumulative_return_pct': [15.60, 12.30, 8.45, 6.78, 5.23],
                'avg_daily_change_pct': [2.23, 1.76, 1.21, 0.97, 0.75],
                'volatility': [3.45, 2.89, 2.34, 2.12, 1.98]
            }),
            14: pd.DataFrame({
                'sector_name': ['水泥建材', '工程机械', '钢铁行业', '有色金属', '煤炭行业'],
                'sector_code': ['BK0425', 'BK0437', 'BK0427', 'BK0478', 'BK0437'],
                'cumulative_return_pct': [19.84, 18.23, 12.67, 10.45, 8.91],
                'avg_daily_change_pct': [1.42, 1.30, 0.90, 0.75, 0.64],
                'volatility': [2.78, 3.12, 2.45, 2.23, 2.01]
            }),
            30: pd.DataFrame({
                'sector_name': ['水泥建材', '工程机械', '钢铁行业', '有色金属', '煤炭行业'],
                'sector_code': ['BK0425', 'BK0437', 'BK0427', 'BK0478', 'BK0437'],
                'cumulative_return_pct': [23.63, 21.45, 15.78, 13.23, 11.67],
                'avg_daily_change_pct': [0.79, 0.72, 0.53, 0.44, 0.39],
                'volatility': [2.34, 2.67, 2.12, 1.98, 1.87]
            })
        }
        
        # 冠军统计数据
        champions = pd.DataFrame({
            'rank': [1, 2, 3, 4, 5],
            'sector_name': ['工程机械', '水泥建材', '钢铁行业', '有色金属', '煤炭行业'],
            'sector_code': ['BK0437', 'BK0425', 'BK0427', 'BK0478', 'BK0437'],
            'champion_count': [25, 18, 12, 8, 6],
            'champion_frequency_pct': [12.5, 9.0, 6.0, 4.0, 3.0],
            'avg_champion_change_pct': [4.23, 3.78, 3.45, 3.12, 2.89]
        })
        
        # 排名频次数据
        ranking_frequency = pd.DataFrame({
            'rank': [1, 2, 3, 4, 5],
            'sector_name': ['工程机械', '水泥建材', '钢铁行业', '有色金属', '煤炭行业'],
            'sector_code': ['BK0437', 'BK0425', 'BK0427', 'BK0478', 'BK0437'],
            'top10_count': [45, 38, 32, 28, 25],
            'top10_frequency_pct': [22.5, 19.0, 16.0, 14.0, 12.5],
            'top5_count': [28, 22, 18, 15, 12],
            'top3_count': [18, 14, 10, 8, 6],
            'champion_count': [25, 18, 12, 8, 6]
        })
        
        print("   ✅ 测试数据创建完成")
        
        # 3. 准备报告数据
        print("\n3. 准备报告数据...")
        analysis_data = {
            'window_performance': window_performance,
            'champions': champions,
            'ranking_frequency': ranking_frequency
        }
        
        print("   ✅ 报告数据准备完成")
        
        # 4. 生成HTML报告
        print("\n4. 生成HTML报告...")
        html_file = report_generator.generate_html_report(
            analysis_data,
            chart_files=[],  # 暂时不包含图表
            report_title="A股板块数据分析测试报告"
        )
        
        if html_file and Path(html_file).exists():
            file_size = Path(html_file).stat().st_size / 1024
            print(f"   ✅ HTML报告生成成功!")
            print(f"   文件路径: {html_file}")
            print(f"   文件大小: {file_size:.1f} KB")
            
            # 检查HTML内容
            with open(html_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 验证关键内容
            checks = [
                ('标题', 'A股板块数据分析测试报告' in content),
                ('时间窗口分析', '时间窗口累计涨跌幅分析' in content),
                ('排名统计', '排名统计分析' in content),
                ('工程机械', '工程机械' in content),
                ('水泥建材', '水泥建材' in content),
                ('CSS样式', '<style>' in content),
                ('表格', '<table>' in content)
            ]
            
            print(f"\n   📋 内容验证:")
            all_passed = True
            for check_name, passed in checks:
                status = "✅" if passed else "❌"
                print(f"     {status} {check_name}")
                if not passed:
                    all_passed = False
            
            if all_passed:
                print(f"\n   🎉 HTML报告内容验证通过!")
                return True
            else:
                print(f"\n   ⚠️ HTML报告内容验证部分失败")
                return False
        else:
            print("   ❌ HTML报告生成失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_csv_export():
    """测试CSV导出功能"""
    print("\n🔍 测试CSV导出功能")
    print("=" * 60)
    
    try:
        from report_generator import ReportGenerator
        
        # 创建报告生成器
        output_dir = 'test_csv_output'
        report_generator = ReportGenerator(output_dir)
        
        # 创建测试数据
        test_data = {
            'window_7d': pd.DataFrame({
                'sector_name': ['工程机械', '水泥建材', '钢铁行业'],
                'sector_code': ['BK0437', 'BK0425', 'BK0427'],
                'cumulative_return_pct': [15.60, 12.30, 8.45]
            }),
            'champions': pd.DataFrame({
                'sector_name': ['工程机械', '水泥建材'],
                'sector_code': ['BK0437', 'BK0425'],
                'champion_count': [25, 18]
            })
        }
        
        # 导出CSV
        csv_files = report_generator.export_to_csv(test_data, 'test_analysis')
        
        if csv_files:
            print(f"✅ CSV导出成功: {len(csv_files)} 个文件")
            for csv_file in csv_files:
                file_path = Path(csv_file)
                if file_path.exists():
                    size_kb = file_path.stat().st_size / 1024
                    print(f"   - {file_path.name} ({size_kb:.1f} KB)")
                else:
                    print(f"   - {file_path.name} (文件不存在)")
            return True
        else:
            print("❌ CSV导出失败")
            return False
            
    except Exception as e:
        print(f"❌ CSV导出测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🧪 HTML报告和数据导出功能测试")
    print("=" * 80)
    
    tests = [
        ('HTML报告生成', test_basic_html_report),
        ('CSV数据导出', test_csv_export)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔬 测试: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
    
    # 测试总结
    print("\n" + "=" * 80)
    print("测试总结")
    print("=" * 80)
    
    print(f"总测试数: {total}")
    print(f"通过数量: {passed}")
    print(f"失败数量: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！HTML报告功能已准备就绪。")
        
        # 显示生成的文件
        print(f"\n📁 生成的测试文件:")
        for output_dir in ['test_html_output', 'test_csv_output']:
            if Path(output_dir).exists():
                files = list(Path(output_dir).glob('*'))
                if files:
                    print(f"   {output_dir}/:")
                    for file in files:
                        size_kb = file.stat().st_size / 1024
                        print(f"     - {file.name} ({size_kb:.1f} KB)")
                        
                        # 如果是HTML文件，提供打开建议
                        if file.suffix.lower() == '.html':
                            print(f"       💡 可以用浏览器打开查看: {file.absolute()}")
        
        print(f"\n💡 现在可以运行主程序测试完整功能:")
        print("   python main.py --quick-mode --verbose --top-n 5")
    else:
        print(f"\n😞 {total - passed} 个测试失败，请检查问题。")
    
    return passed == total

if __name__ == '__main__':
    main()
