#!/usr/bin/env python3
"""
TimeWindowAnalyzer模块测试脚本
"""

import sys
import os
sys.path.append('src')

from data_loader import DataLoader
from time_window_analyzer import TimeWindowAnalyzer
import config
import pandas as pd

def test_time_window_analyzer():
    """测试TimeWindowAnalyzer功能"""
    print("=" * 80)
    print("TimeWindowAnalyzer模块测试")
    print("=" * 80)
    
    try:
        # 1. 加载测试数据
        print("1. 加载测试数据...")
        loader = DataLoader()
        # 只加载前30个文件进行测试
        loader.file_list = loader.file_list[:30]
        data = loader.load_all_data()
        print(f"   ✓ 数据加载完成，形状: {data.shape}")
        
        # 2. 初始化TimeWindowAnalyzer
        print("\n2. 初始化TimeWindowAnalyzer...")
        analyzer = TimeWindowAnalyzer(data)
        print(f"   ✓ 分析器初始化完成")
        print(f"   ✓ 数据日期范围: {analyzer.date_range[0]} 到 {analyzer.date_range[1]}")
        print(f"   ✓ 总交易日数: {len(analyzer.available_dates)}")
        
        # 3. 测试时间窗口计算
        print("\n3. 测试时间窗口表现计算...")
        end_date = analyzer.available_dates[-1]  # 使用最后一个交易日
        window_days = 7
        
        performance = analyzer.calculate_window_performance(end_date, window_days)
        print(f"   ✓ 计算完成，共 {len(performance)} 个板块")
        print(f"   ✓ 时间窗口: {window_days} 个交易日")
        
        # 显示前5名
        if not performance.empty:
            print("\n   前5名板块表现:")
            top5 = performance.head(5)
            for idx, row in top5.iterrows():
                print(f"     {idx+1}. {row['sector_name']} ({row['sector_code']}): {row['cumulative_return_pct']:.2f}%")
        
        # 4. 测试获取表现最佳板块
        print("\n4. 测试获取表现最佳板块...")
        top_performers = analyzer.get_top_performers(performance, 10)
        print(f"   ✓ 获取前10名板块完成")
        print(f"   ✓ 最佳表现: {top_performers.iloc[0]['sector_name']} ({top_performers.iloc[0]['cumulative_return_pct']:.2f}%)")
        
        # 5. 测试多时间窗口计算
        print("\n5. 测试多时间窗口计算...")
        window_list = [3, 7, 14]
        multi_results = analyzer.calculate_multiple_windows(end_date, window_list)
        print(f"   ✓ 计算 {len(window_list)} 个时间窗口完成")
        
        for days, result in multi_results.items():
            if not result.empty:
                best_sector = result.iloc[0]
                print(f"     {days}日窗口最佳: {best_sector['sector_name']} ({best_sector['cumulative_return_pct']:.2f}%)")
        
        # 6. 测试时间窗口摘要
        print("\n6. 测试时间窗口摘要...")
        summary = analyzer.get_window_summary(end_date, 7)
        if 'error' not in summary:
            stats = summary['performance_stats']
            print(f"   ✓ 摘要生成完成")
            print(f"     平均收益: {stats['avg_return']:.2f}%")
            print(f"     正收益板块: {stats['positive_sectors']}/{stats['total_sectors']}")
            print(f"     最大收益: {stats['max_return']:.2f}%")
            print(f"     最小收益: {stats['min_return']:.2f}%")
        
        # 7. 测试缓存功能
        print("\n7. 测试缓存功能...")
        cache_info = analyzer.get_cache_info()
        print(f"   ✓ 缓存大小: {cache_info['cache_size']}")
        
        # 清除缓存
        analyzer.clear_cache()
        cache_info_after = analyzer.get_cache_info()
        print(f"   ✓ 清除后缓存大小: {cache_info_after['cache_size']}")
        
        # 8. 测试跨年度对比（如果有足够数据）
        print("\n8. 测试跨年度对比...")
        if len(analyzer.available_dates) > 365:  # 如果有超过一年的数据
            yearly_comparison = analyzer.compare_yearly_windows(7)
            if 'error' not in yearly_comparison:
                years = list(yearly_comparison['yearly_stats'].keys())
                print(f"   ✓ 跨年度对比完成，涵盖年份: {years}")
                
                for year, stats in yearly_comparison['yearly_stats'].items():
                    print(f"     {year}年: 平均收益 {stats['avg_return']:.2f}%, 最佳板块 {stats['top_sector']}")
            else:
                print(f"   ⚠ 跨年度对比: {yearly_comparison['error']}")
        else:
            print("   ⚠ 数据不足，跳过跨年度对比测试")
        
        print("\n" + "=" * 80)
        print("测试完成！TimeWindowAnalyzer模块工作正常。")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        print(f"\n测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    test_time_window_analyzer()
