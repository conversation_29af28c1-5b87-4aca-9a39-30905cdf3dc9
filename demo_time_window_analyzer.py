#!/usr/bin/env python3
"""
TimeWindowAnalyzer模块演示脚本
展示时间窗口分析功能的完整使用
"""

import sys
import os
sys.path.append('src')

from data_loader import DataLoader
from time_window_analyzer import TimeWindowAnalyzer
import config
import pandas as pd

def main():
    """主演示函数"""
    print("=" * 90)
    print("A股板块数据分析程序 - TimeWindowAnalyzer模块演示")
    print("=" * 90)
    
    try:
        # 1. 加载数据
        print("\n1. 加载演示数据...")
        loader = DataLoader()
        # 加载前50个文件进行演示
        loader.file_list = loader.file_list[:50]
        data = loader.load_all_data()
        print(f"   ✓ 数据加载完成，形状: {data.shape}")
        
        # 2. 初始化分析器
        print("\n2. 初始化时间窗口分析器...")
        analyzer = TimeWindowAnalyzer(data)
        print(f"   ✓ 分析器初始化完成")
        print(f"   ✓ 数据日期范围: {analyzer.date_range[0]} 到 {analyzer.date_range[1]}")
        print(f"   ✓ 可用交易日: {len(analyzer.available_dates)} 天")
        
        # 3. 单一时间窗口分析
        print("\n3. 单一时间窗口分析演示...")
        end_date = analyzer.available_dates[-1]
        window_days = 14
        
        print(f"   分析参数: 结束日期={end_date.strftime('%Y-%m-%d')}, 窗口={window_days}个交易日")
        
        performance = analyzer.calculate_window_performance(end_date, window_days)
        print(f"   ✓ 分析完成，共 {len(performance)} 个板块")
        
        # 显示详细结果
        print(f"\n   📊 {window_days}日时间窗口表现排行榜 (前10名):")
        print("   " + "-" * 85)
        print("   排名  板块名称           板块代码    累计收益率   平均日收益率   波动率")
        print("   " + "-" * 85)
        
        top10 = analyzer.get_top_performers(performance, 10)
        for idx, row in top10.iterrows():
            print(f"   {row['rank']:2d}    {row['sector_name']:<12} {row['sector_code']:<8} "
                  f"{row['cumulative_return_pct']:8.2f}%   {row['avg_daily_change_pct']:8.2f}%   "
                  f"{row['volatility']:6.2f}%")
        print("   " + "-" * 85)
        
        # 4. 多时间窗口对比分析
        print(f"\n4. 多时间窗口对比分析...")
        window_list = [3, 7, 14, 30]
        multi_results = analyzer.calculate_multiple_windows(end_date, window_list)
        
        print(f"   📈 不同时间窗口下的最佳板块表现:")
        print("   " + "-" * 70)
        print("   时间窗口    最佳板块              累计收益率    交易日数")
        print("   " + "-" * 70)
        
        for days in sorted(window_list):
            if days in multi_results and not multi_results[days].empty:
                best = multi_results[days].iloc[0]
                print(f"   {days:4d}日     {best['sector_name']:<15} {best['cumulative_return_pct']:8.2f}%    "
                      f"{best['trading_days']:4d}天")
        print("   " + "-" * 70)
        
        # 5. 时间窗口摘要分析
        print(f"\n5. 时间窗口摘要分析...")
        summary = analyzer.get_window_summary(end_date, 14)
        
        if 'error' not in summary:
            window_info = summary['window_info']
            stats = summary['performance_stats']
            
            print(f"   📋 14日时间窗口摘要:")
            print(f"   ├─ 时间范围: {window_info['start_date'].strftime('%Y-%m-%d')} 到 "
                  f"{window_info['end_date_actual'].strftime('%Y-%m-%d')}")
            print(f"   ├─ 实际交易日: {window_info['actual_trading_days']} 天")
            print(f"   ├─ 分析板块数: {stats['total_sectors']} 个")
            print(f"   ├─ 平均收益率: {stats['avg_return']:.2f}%")
            print(f"   ├─ 中位数收益率: {stats['median_return']:.2f}%")
            print(f"   ├─ 最大收益率: {stats['max_return']:.2f}%")
            print(f"   ├─ 最小收益率: {stats['min_return']:.2f}%")
            print(f"   ├─ 收益率标准差: {stats['std_return']:.2f}%")
            print(f"   ├─ 正收益板块: {stats['positive_sectors']} 个 "
                  f"({stats['positive_sectors']/stats['total_sectors']*100:.1f}%)")
            print(f"   └─ 负收益板块: {stats['negative_sectors']} 个 "
                  f"({stats['negative_sectors']/stats['total_sectors']*100:.1f}%)")
        
        # 6. 特定板块的最佳时间窗口分析
        print(f"\n6. 特定板块的最佳时间窗口分析...")
        if not performance.empty:
            # 选择一个表现较好的板块进行分析
            sample_sector = performance.iloc[2]['sector_code']  # 选择第3名的板块
            sample_name = performance.iloc[2]['sector_name']
            
            print(f"   分析板块: {sample_name} ({sample_sector})")
            
            best_windows = analyzer.find_best_performing_windows(sample_sector, 7, 5)
            
            if not best_windows.empty:
                print(f"   📅 该板块7日时间窗口最佳表现期 (前5名):")
                print("   " + "-" * 75)
                print("   排名  时间窗口                    累计收益率   平均日收益率   波动率")
                print("   " + "-" * 75)
                
                for idx, row in best_windows.iterrows():
                    window_str = f"{row['window_start'].strftime('%m-%d')} 到 {row['window_end'].strftime('%m-%d')}"
                    print(f"   {row['rank']:2d}    {window_str:<20} {row['cumulative_return_pct']:8.2f}%   "
                          f"{row['avg_daily_change_pct']:8.2f}%   {row['volatility']:6.2f}%")
                print("   " + "-" * 75)
        
        # 7. 缓存性能展示
        print(f"\n7. 缓存性能展示...")
        cache_info = analyzer.get_cache_info()
        print(f"   ✓ 当前缓存项目: {cache_info['cache_size']} 个")
        print(f"   ✓ 缓存提升了重复查询的性能")
        
        # 演示缓存效果
        import time
        start_time = time.time()
        _ = analyzer.calculate_window_performance(end_date, 14)  # 第二次调用，使用缓存
        cached_time = time.time() - start_time
        print(f"   ✓ 缓存查询耗时: {cached_time*1000:.2f} 毫秒")
        
        print("\n" + "=" * 90)
        print("演示完成！TimeWindowAnalyzer模块功能展示结束。")
        print("\n核心功能总结:")
        print("  ✓ 灵活的时间窗口计算 (支持任意天数)")
        print("  ✓ 累计涨跌幅分析 (复合增长计算)")
        print("  ✓ 多时间窗口对比分析")
        print("  ✓ 跨年度同期对比分析")
        print("  ✓ 板块表现排行榜生成")
        print("  ✓ 特定板块最佳时间窗口查找")
        print("  ✓ 统计摘要和性能指标")
        print("  ✓ 智能缓存机制")
        print("  ✓ 交易日计算 (排除周末)")
        print("=" * 90)
        
    except Exception as e:
        print(f"\n演示过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
