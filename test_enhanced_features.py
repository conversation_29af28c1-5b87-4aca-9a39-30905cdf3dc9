#!/usr/bin/env python3
"""
测试增强功能
"""

import sys
import os
from pathlib import Path

# 添加src到路径
sys.path.insert(0, 'src')

def test_historical_analyzer():
    """测试历史分析器"""
    print("🔍 测试历史分析器")
    print("=" * 50)
    
    try:
        # 导入必要模块
        from data_loader import DataLoader
        from historical_analyzer import HistoricalAnalyzer
        import pandas as pd
        
        # 1. 加载数据
        print("1. 加载测试数据...")
        loader = DataLoader()
        
        # 只使用少量文件进行测试
        loader.file_list = loader.file_list[-20:]  # 最近20个文件
        print(f"   使用 {len(loader.file_list)} 个文件进行测试")
        
        data = loader.load_all_data()
        print(f"   数据形状: {data.shape}")
        print(f"   日期范围: {data.index.get_level_values('date').min()} 到 {data.index.get_level_values('date').max()}")
        
        # 2. 初始化历史分析器
        print("\n2. 初始化历史分析器...")
        historical_analyzer = HistoricalAnalyzer(data)
        print(f"   涵盖年份: {sorted(historical_analyzer.yearly_data.keys())}")
        
        # 3. 测试历史分析
        print("\n3. 测试历史分析...")
        target_date = historical_analyzer.available_dates[-1]  # 使用最新日期
        window_days_list = [7, 14]  # 简化测试
        
        print(f"   目标日期: {target_date}")
        print(f"   时间窗口: {window_days_list}")
        
        results = historical_analyzer.calculate_historical_windows(
            target_date, window_days_list
        )
        
        # 4. 检查结果
        print("\n4. 检查分析结果...")
        if results:
            print(f"   ✅ 历史分析完成")
            print(f"   目标日期: {results.get('target_date', 'N/A')}")
            print(f"   历史日期数: {len(results.get('historical_dates', {}))}")
            
            # 检查窗口分析
            window_analysis = results.get('window_analysis', {})
            for window_days, yearly_data in window_analysis.items():
                print(f"   {window_days}日窗口: {len(yearly_data)} 年数据")
            
            # 检查年度对比
            yearly_comparison = results.get('yearly_comparison', {})
            for window_days, comparison in yearly_comparison.items():
                best_year = comparison.get('best_year', {})
                if best_year:
                    print(f"   {window_days}日窗口最佳年份: {best_year['year']} ({best_year['avg_return']:.2f}%)")
            
            print("   ✅ 历史分析功能正常")
        else:
            print("   ❌ 历史分析结果为空")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ 历史分析测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_report_generator():
    """测试报告生成器"""
    print("\n🔍 测试报告生成器")
    print("=" * 50)
    
    try:
        from report_generator import ReportGenerator
        import pandas as pd
        
        # 1. 初始化报告生成器
        print("1. 初始化报告生成器...")
        report_generator = ReportGenerator('test_output')
        print("   ✅ 报告生成器初始化成功")
        
        # 2. 创建测试数据
        print("\n2. 创建测试数据...")
        test_data = {
            'window_performance': {
                7: pd.DataFrame({
                    'sector_name': ['板块A', '板块B', '板块C'],
                    'sector_code': ['001', '002', '003'],
                    'cumulative_return_pct': [5.2, 3.1, -1.5],
                    'avg_daily_change_pct': [0.7, 0.4, -0.2],
                    'volatility': [2.1, 1.8, 2.5]
                }),
                14: pd.DataFrame({
                    'sector_name': ['板块A', '板块C', '板块B'],
                    'sector_code': ['001', '003', '002'],
                    'cumulative_return_pct': [8.5, 2.1, 1.8],
                    'avg_daily_change_pct': [0.6, 0.15, 0.13],
                    'volatility': [2.3, 2.0, 1.9]
                })
            },
            'champions': pd.DataFrame({
                'sector_name': ['板块A', '板块B'],
                'sector_code': ['001', '002'],
                'champion_count': [5, 3],
                'champion_frequency_pct': [25.0, 15.0],
                'avg_champion_change_pct': [3.2, 2.8]
            }),
            'historical_analysis': {
                'target_date': '2025-07-22',
                'target_month_day': '07-22',
                'historical_dates': {2023: '2023-07-22', 2024: '2024-07-22', 2025: '2025-07-22'},
                'yearly_comparison': {
                    7: {
                        'best_year': {'year': 2024, 'avg_return': 2.5, 'top_sector': '板块A'},
                        'worst_year': {'year': 2023, 'avg_return': -0.5, 'top_sector': '板块B'},
                        'yearly_stats': {
                            2023: {'avg_return': -0.5, 'max_return': 2.1, 'std_return': 1.8, 'positive_sectors': 2, 'negative_sectors': 1, 'top_sector': '板块B'},
                            2024: {'avg_return': 2.5, 'max_return': 5.2, 'std_return': 2.1, 'positive_sectors': 3, 'negative_sectors': 0, 'top_sector': '板块A'},
                            2025: {'avg_return': 1.2, 'max_return': 3.8, 'std_return': 1.9, 'positive_sectors': 2, 'negative_sectors': 1, 'top_sector': '板块A'}
                        }
                    }
                },
                'trend_analysis': {
                    7: {
                        'avg_return_trend': {'trend': 'increasing', 'slope': 1.25, 'correlation': 0.85, 'change_per_year': 1.25, 'total_change': 2.5},
                        'sector_consistency': {
                            'consistent_performers': [('001', {'consistency_rate': 1.0, 'appearances': 3, 'total_years': 3})],
                            'total_sectors_analyzed': 3,
                            'consistency_threshold': 0.5
                        }
                    }
                }
            }
        }
        print("   ✅ 测试数据创建成功")
        
        # 3. 测试HTML报告生成
        print("\n3. 测试HTML报告生成...")
        html_file = report_generator.generate_html_report(
            test_data, 
            [],  # 没有图表文件
            "测试分析报告"
        )
        
        if html_file and Path(html_file).exists():
            file_size = Path(html_file).stat().st_size / 1024
            print(f"   ✅ HTML报告生成成功: {Path(html_file).name}")
            print(f"   文件大小: {file_size:.1f} KB")
        else:
            print("   ❌ HTML报告生成失败")
            return False
        
        # 4. 测试CSV导出
        print("\n4. 测试CSV导出...")
        csv_files = report_generator.export_to_csv({
            'window_7d': test_data['window_performance'][7],
            'champions': test_data['champions']
        }, 'test_export')
        
        if csv_files:
            print(f"   ✅ CSV导出成功: {len(csv_files)} 个文件")
            for csv_file in csv_files:
                print(f"     - {Path(csv_file).name}")
        else:
            print("   ❌ CSV导出失败")
            return False
        
        print("   ✅ 报告生成器功能正常")
        return True
        
    except Exception as e:
        print(f"   ❌ 报告生成器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_main_program():
    """测试主程序集成"""
    print("\n🔍 测试主程序集成")
    print("=" * 50)
    
    try:
        import subprocess
        
        # 测试帮助信息
        print("1. 测试帮助信息...")
        result = subprocess.run(
            ['python', 'main.py', '--help'],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0 and '--enable-historical' in result.stdout:
            print("   ✅ 新参数已添加到帮助信息")
        else:
            print("   ❌ 帮助信息测试失败")
            return False
        
        print("   ✅ 主程序集成正常")
        return True
        
    except Exception as e:
        print(f"   ❌ 主程序测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🧪 增强功能测试")
    print("=" * 80)
    
    tests = [
        ('历史分析器', test_historical_analyzer),
        ('报告生成器', test_report_generator),
        ('主程序集成', test_main_program)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔬 测试: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
    
    # 测试总结
    print("\n" + "=" * 80)
    print("测试总结")
    print("=" * 80)
    
    print(f"总测试数: {total}")
    print(f"通过数量: {passed}")
    print(f"失败数量: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！增强功能已准备就绪。")
        print("\n💡 现在可以使用以下命令测试新功能:")
        print("   python main.py --quick-mode --enable-historical --verbose")
    else:
        print(f"\n😞 {total - passed} 个测试失败，请检查问题。")
    
    return passed == total

if __name__ == '__main__':
    main()
