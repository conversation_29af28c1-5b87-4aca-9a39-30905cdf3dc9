"""
数据可视化模块
负责生成各种分析图表
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import seaborn as sns
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Optional, Dict, List, Tuple, Union
import logging
import os
import sys
from datetime import datetime
import warnings

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import config

# 忽略matplotlib的警告
warnings.filterwarnings('ignore', category=UserWarning)

class Visualizer:
    """可视化器类"""

    def __init__(self, output_dir: str = 'output'):
        """
        初始化可视化器

        Args:
            output_dir: 输出目录路径
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

        self.logger = self._setup_logger()

        # 设置中文字体和样式
        self._setup_matplotlib_style()

        # 图表配置
        self.chart_config = config.CHART_CONFIG.copy()

        self.logger.info(f"可视化器初始化完成，输出目录: {self.output_dir}")

    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(__name__)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger

    def _setup_matplotlib_style(self):
        """设置matplotlib样式和中文字体"""
        try:
            # 设置seaborn样式
            if hasattr(sns, 'set_style'):
                sns.set_style("whitegrid")

            # 尝试设置中文字体
            chinese_fonts = [
                'SimHei',           # Windows黑体
                'Microsoft YaHei', # Windows微软雅黑
                'DejaVu Sans',     # Linux
                'Arial Unicode MS', # macOS
                'PingFang SC',     # macOS
                'Noto Sans CJK SC' # 通用
            ]

            font_found = False
            for font_name in chinese_fonts:
                try:
                    plt.rcParams['font.sans-serif'] = [font_name]
                    # 测试字体是否可用
                    fig, ax = plt.subplots(figsize=(1, 1))
                    ax.text(0.5, 0.5, '测试', fontsize=12)
                    plt.close(fig)
                    font_found = True
                    self.logger.info(f"使用中文字体: {font_name}")
                    break
                except:
                    continue

            if not font_found:
                self.logger.warning("未找到合适的中文字体，可能影响中文显示")

            # 设置其他matplotlib参数
            plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
            plt.rcParams['figure.figsize'] = self.chart_config['figsize']
            plt.rcParams['figure.dpi'] = self.chart_config['dpi']
            plt.rcParams['font.size'] = self.chart_config['font_size']

        except Exception as e:
            self.logger.warning(f"设置matplotlib样式时出错: {str(e)}")

    def _save_figure(self, fig, filename: str, title: str = "") -> str:
        """
        保存图表到文件

        Args:
            fig: matplotlib图表对象
            filename: 文件名（不含扩展名）
            title: 图表标题

        Returns:
            保存的文件路径
        """
        try:
            # 添加时间戳避免文件名冲突
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            full_filename = f"{filename}_{timestamp}.{self.chart_config['format']}"
            filepath = self.output_dir / full_filename

            # 设置图表标题
            if title:
                fig.suptitle(title, fontsize=16, fontweight='bold', y=0.95)

            # 调整布局
            fig.tight_layout()

            # 保存图表
            fig.savefig(
                filepath,
                format=self.chart_config['format'],
                dpi=self.chart_config['dpi'],
                bbox_inches='tight',
                facecolor='white',
                edgecolor='none'
            )

            self.logger.info(f"图表已保存: {filepath}")
            return str(filepath)

        except Exception as e:
            self.logger.error(f"保存图表失败: {str(e)}")
            return ""

    def plot_window_performance(self, data: pd.DataFrame, window_days: int,
                               top_n: int = 15, title: str = None) -> str:
        """
        绘制时间窗口表现图

        Args:
            data: 时间窗口表现数据
            window_days: 时间窗口天数
            top_n: 显示前N名
            title: 自定义标题

        Returns:
            保存的文件路径
        """
        if data.empty:
            self.logger.warning("时间窗口表现数据为空，无法绘制图表")
            return ""

        try:
            # 取前N名数据
            plot_data = data.head(top_n).copy()

            # 创建图表
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 12))

            # 1. 累计收益率柱状图
            colors = plt.cm.RdYlGn(np.linspace(0.3, 0.9, len(plot_data)))
            bars1 = ax1.barh(range(len(plot_data)), plot_data['cumulative_return_pct'],
                            color=colors, alpha=0.8, edgecolor='black', linewidth=0.5)

            # 设置y轴标签
            ax1.set_yticks(range(len(plot_data)))
            ax1.set_yticklabels([f"{i+1}. {name}" for i, name in enumerate(plot_data['sector_name'])],
                               fontsize=10)
            ax1.invert_yaxis()  # 反转y轴，使第1名在顶部

            # 添加数值标签
            for i, (bar, value) in enumerate(zip(bars1, plot_data['cumulative_return_pct'])):
                ax1.text(bar.get_width() + 0.1, bar.get_y() + bar.get_height()/2,
                        f'{value:.2f}%', ha='left', va='center', fontsize=9, fontweight='bold')

            ax1.set_xlabel('累计收益率 (%)', fontsize=12, fontweight='bold')
            ax1.set_title(f'{window_days}日时间窗口累计收益率排行榜 (前{top_n}名)',
                         fontsize=14, fontweight='bold', pad=20)
            ax1.grid(True, alpha=0.3, axis='x')

            # 2. 波动率vs收益率散点图
            scatter = ax2.scatter(plot_data['volatility'], plot_data['cumulative_return_pct'],
                                 c=plot_data['cumulative_return_pct'], cmap='RdYlGn',
                                 s=100, alpha=0.7, edgecolors='black', linewidth=0.5)

            # 添加板块名称标签
            for idx, row in plot_data.iterrows():
                ax2.annotate(row['sector_name'],
                           (row['volatility'], row['cumulative_return_pct']),
                           xytext=(5, 5), textcoords='offset points',
                           fontsize=8, alpha=0.8)

            ax2.set_xlabel('波动率 (%)', fontsize=12, fontweight='bold')
            ax2.set_ylabel('累计收益率 (%)', fontsize=12, fontweight='bold')
            ax2.set_title('收益率 vs 波动率分布图', fontsize=14, fontweight='bold', pad=20)
            ax2.grid(True, alpha=0.3)

            # 添加颜色条
            cbar = plt.colorbar(scatter, ax=ax2)
            cbar.set_label('累计收益率 (%)', fontsize=10)

            # 保存图表
            chart_title = title or f"A股板块{window_days}日时间窗口表现分析"
            filepath = self._save_figure(fig, f"window_performance_{window_days}d", chart_title)

            plt.close(fig)
            return filepath

        except Exception as e:
            self.logger.error(f"绘制时间窗口表现图时出错: {str(e)}")
            return ""

    def plot_ranking_frequency(self, frequency_data: pd.DataFrame,
                              top_n: int = 15, title: str = None) -> str:
        """
        绘制排名频次图

        Args:
            frequency_data: 排名频次数据
            top_n: 显示前N名
            title: 自定义标题

        Returns:
            保存的文件路径
        """
        if frequency_data.empty:
            self.logger.warning("排名频次数据为空，无法绘制图表")
            return ""

        try:
            # 取前N名数据
            plot_data = frequency_data.head(top_n).copy()

            # 创建图表
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

            # 1. 前10频次柱状图
            colors1 = plt.cm.Blues(np.linspace(0.4, 0.9, len(plot_data)))
            bars1 = ax1.barh(range(len(plot_data)), plot_data['top10_count'],
                            color=colors1, alpha=0.8, edgecolor='black', linewidth=0.5)

            ax1.set_yticks(range(len(plot_data)))
            ax1.set_yticklabels([f"{i+1}. {name}" for i, name in enumerate(plot_data['sector_name'])],
                               fontsize=9)
            ax1.invert_yaxis()

            # 添加数值标签
            for bar, value in zip(bars1, plot_data['top10_count']):
                ax1.text(bar.get_width() + 0.5, bar.get_y() + bar.get_height()/2,
                        f'{int(value)}', ha='left', va='center', fontsize=8, fontweight='bold')

            ax1.set_xlabel('前10次数', fontsize=11, fontweight='bold')
            ax1.set_title(f'前10名频次排行榜 (前{top_n}名)', fontsize=12, fontweight='bold')
            ax1.grid(True, alpha=0.3, axis='x')

            # 2. 冠军次数柱状图
            colors2 = plt.cm.Reds(np.linspace(0.4, 0.9, len(plot_data)))
            bars2 = ax2.barh(range(len(plot_data)), plot_data['champion_count'],
                            color=colors2, alpha=0.8, edgecolor='black', linewidth=0.5)

            ax2.set_yticks(range(len(plot_data)))
            ax2.set_yticklabels([f"{i+1}. {name}" for i, name in enumerate(plot_data['sector_name'])],
                               fontsize=9)
            ax2.invert_yaxis()

            # 添加数值标签
            for bar, value in zip(bars2, plot_data['champion_count']):
                ax2.text(bar.get_width() + 0.1, bar.get_y() + bar.get_height()/2,
                        f'{int(value)}', ha='left', va='center', fontsize=8, fontweight='bold')

            ax2.set_xlabel('冠军次数', fontsize=11, fontweight='bold')
            ax2.set_title(f'冠军次数排行榜 (前{top_n}名)', fontsize=12, fontweight='bold')
            ax2.grid(True, alpha=0.3, axis='x')

            # 3. 频率对比堆叠柱状图
            categories = ['前3', '前5', '前10']
            freq_data = np.array([
                plot_data['top3_frequency_pct'].values,
                plot_data['top5_frequency_pct'].values - plot_data['top3_frequency_pct'].values,
                plot_data['top10_frequency_pct'].values - plot_data['top5_frequency_pct'].values
            ])

            bottom = np.zeros(len(plot_data))
            colors3 = ['#ff9999', '#66b3ff', '#99ff99']

            for i, (category, color) in enumerate(zip(categories, colors3)):
                ax3.barh(range(len(plot_data)), freq_data[i], left=bottom,
                        label=category, color=color, alpha=0.8, edgecolor='black', linewidth=0.3)
                bottom += freq_data[i]

            ax3.set_yticks(range(len(plot_data)))
            ax3.set_yticklabels([f"{name}" for name in plot_data['sector_name']], fontsize=9)
            ax3.invert_yaxis()
            ax3.set_xlabel('频率 (%)', fontsize=11, fontweight='bold')
            ax3.set_title('排名频率分布', fontsize=12, fontweight='bold')
            ax3.legend(loc='lower right')
            ax3.grid(True, alpha=0.3, axis='x')

            # 4. 平均排名散点图
            scatter = ax4.scatter(plot_data['avg_rank_in_top10'], plot_data['top10_frequency_pct'],
                                 c=plot_data['champion_count'], cmap='Reds',
                                 s=80, alpha=0.7, edgecolors='black', linewidth=0.5)

            # 添加板块名称标签
            for idx, row in plot_data.iterrows():
                ax4.annotate(row['sector_name'],
                           (row['avg_rank_in_top10'], row['top10_frequency_pct']),
                           xytext=(3, 3), textcoords='offset points',
                           fontsize=7, alpha=0.8)

            ax4.set_xlabel('平均排名', fontsize=11, fontweight='bold')
            ax4.set_ylabel('前10频率 (%)', fontsize=11, fontweight='bold')
            ax4.set_title('平均排名 vs 前10频率', fontsize=12, fontweight='bold')
            ax4.grid(True, alpha=0.3)

            # 添加颜色条
            cbar = plt.colorbar(scatter, ax=ax4)
            cbar.set_label('冠军次数', fontsize=9)

            # 保存图表
            chart_title = title or "A股板块排名频次统计分析"
            filepath = self._save_figure(fig, "ranking_frequency", chart_title)

            plt.close(fig)
            return filepath

        except Exception as e:
            self.logger.error(f"绘制排名频次图时出错: {str(e)}")
            return ""

    def plot_champion_distribution(self, champion_data: pd.DataFrame,
                                  top_n: int = 12, title: str = None) -> str:
        """
        绘制冠军分布图

        Args:
            champion_data: 冠军统计数据
            top_n: 显示前N名
            title: 自定义标题

        Returns:
            保存的文件路径
        """
        if champion_data.empty:
            self.logger.warning("冠军数据为空，无法绘制图表")
            return ""

        try:
            # 取前N名数据
            plot_data = champion_data.head(top_n).copy()

            # 创建图表
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

            # 1. 冠军次数饼图
            colors_pie = plt.cm.Set3(np.linspace(0, 1, len(plot_data)))
            wedges, texts, autotexts = ax1.pie(plot_data['champion_count'],
                                              labels=plot_data['sector_name'],
                                              colors=colors_pie,
                                              autopct='%1.1f%%',
                                              startangle=90,
                                              textprops={'fontsize': 8})

            ax1.set_title(f'冠军次数分布 (前{top_n}名)', fontsize=12, fontweight='bold')

            # 2. 冠军频率柱状图
            colors_bar = plt.cm.Oranges(np.linspace(0.4, 0.9, len(plot_data)))
            bars = ax2.bar(range(len(plot_data)), plot_data['champion_frequency_pct'],
                          color=colors_bar, alpha=0.8, edgecolor='black', linewidth=0.5)

            ax2.set_xticks(range(len(plot_data)))
            ax2.set_xticklabels([name[:6] + '...' if len(name) > 6 else name
                                for name in plot_data['sector_name']],
                               rotation=45, ha='right', fontsize=9)

            # 添加数值标签
            for bar, value in zip(bars, plot_data['champion_frequency_pct']):
                ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                        f'{value:.2f}%', ha='center', va='bottom', fontsize=8, fontweight='bold')

            ax2.set_ylabel('冠军频率 (%)', fontsize=11, fontweight='bold')
            ax2.set_title(f'冠军频率排行榜 (前{top_n}名)', fontsize=12, fontweight='bold')
            ax2.grid(True, alpha=0.3, axis='y')

            # 3. 平均冠军涨跌幅 vs 冠军次数散点图
            scatter = ax3.scatter(plot_data['champion_count'], plot_data['avg_champion_change_pct'],
                                 c=plot_data['max_champion_change_pct'], cmap='Reds',
                                 s=100, alpha=0.7, edgecolors='black', linewidth=0.5)

            # 添加板块名称标签
            for idx, row in plot_data.iterrows():
                ax3.annotate(row['sector_name'],
                           (row['champion_count'], row['avg_champion_change_pct']),
                           xytext=(3, 3), textcoords='offset points',
                           fontsize=8, alpha=0.8)

            ax3.set_xlabel('冠军次数', fontsize=11, fontweight='bold')
            ax3.set_ylabel('平均冠军涨跌幅 (%)', fontsize=11, fontweight='bold')
            ax3.set_title('冠军次数 vs 平均涨跌幅', fontsize=12, fontweight='bold')
            ax3.grid(True, alpha=0.3)

            # 添加颜色条
            cbar = plt.colorbar(scatter, ax=ax3)
            cbar.set_label('最大冠军涨跌幅 (%)', fontsize=9)

            # 4. 冠军涨跌幅分布箱线图
            champion_changes = []
            labels = []

            for idx, row in plot_data.head(8).iterrows():  # 只显示前8个以免过于拥挤
                # 这里简化处理，使用平均值和标准差生成模拟分布
                # 在实际应用中，应该使用真实的历史冠军涨跌幅数据
                mean_change = row['avg_champion_change_pct']
                std_change = abs(mean_change) * 0.3  # 假设标准差
                simulated_data = np.random.normal(mean_change, std_change, row['champion_count'])
                champion_changes.append(simulated_data)
                labels.append(row['sector_name'][:8])

            bp = ax4.boxplot(champion_changes, labels=labels, patch_artist=True)

            # 设置箱线图颜色
            colors_box = plt.cm.Pastel1(np.linspace(0, 1, len(champion_changes)))
            for patch, color in zip(bp['boxes'], colors_box):
                patch.set_facecolor(color)
                patch.set_alpha(0.7)

            ax4.set_xticklabels(labels, rotation=45, ha='right', fontsize=9)
            ax4.set_ylabel('冠军涨跌幅 (%)', fontsize=11, fontweight='bold')
            ax4.set_title('冠军涨跌幅分布', fontsize=12, fontweight='bold')
            ax4.grid(True, alpha=0.3, axis='y')

            # 保存图表
            chart_title = title or "A股板块冠军分布统计分析"
            filepath = self._save_figure(fig, "champion_distribution", chart_title)

            plt.close(fig)
            return filepath

        except Exception as e:
            self.logger.error(f"绘制冠军分布图时出错: {str(e)}")
            return ""

    def plot_time_series_trends(self, data: pd.DataFrame, sectors: List[str],
                               metric: str = 'change_pct', title: str = None) -> str:
        """
        绘制时间序列趋势图

        Args:
            data: 包含时间序列数据的DataFrame
            sectors: 要显示的板块列表
            metric: 要显示的指标
            title: 自定义标题

        Returns:
            保存的文件路径
        """
        if data.empty or not sectors:
            self.logger.warning("时间序列数据为空或未指定板块，无法绘制图表")
            return ""

        try:
            # 创建图表
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 10))

            # 准备数据
            colors = plt.cm.tab10(np.linspace(0, 1, len(sectors)))

            # 1. 时间序列线图
            for i, sector in enumerate(sectors):
                if isinstance(data.index, pd.MultiIndex):
                    sector_data = data[data.index.get_level_values('sector_code') == sector]
                    if not sector_data.empty:
                        dates = sector_data.index.get_level_values('date')
                        values = sector_data[metric]
                        sector_name = sector_data['sector_name'].iloc[0] if 'sector_name' in sector_data.columns else sector

                        ax1.plot(dates, values, color=colors[i], linewidth=2,
                                label=sector_name, alpha=0.8)

            ax1.set_xlabel('日期', fontsize=12, fontweight='bold')
            ax1.set_ylabel(f'{metric} (%)', fontsize=12, fontweight='bold')
            ax1.set_title(f'板块{metric}时间序列趋势', fontsize=14, fontweight='bold')
            ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
            ax1.grid(True, alpha=0.3)
            ax1.tick_params(axis='x', rotation=45)

            # 2. 累计收益率对比
            for i, sector in enumerate(sectors):
                if isinstance(data.index, pd.MultiIndex):
                    sector_data = data[data.index.get_level_values('sector_code') == sector]
                    if not sector_data.empty:
                        dates = sector_data.index.get_level_values('date')
                        values = sector_data[metric]
                        # 计算累计收益率
                        cumulative_returns = (1 + values / 100).cumprod() - 1
                        sector_name = sector_data['sector_name'].iloc[0] if 'sector_name' in sector_data.columns else sector

                        ax2.plot(dates, cumulative_returns * 100, color=colors[i],
                                linewidth=2, label=sector_name, alpha=0.8)

            ax2.set_xlabel('日期', fontsize=12, fontweight='bold')
            ax2.set_ylabel('累计收益率 (%)', fontsize=12, fontweight='bold')
            ax2.set_title('板块累计收益率对比', fontsize=14, fontweight='bold')
            ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
            ax2.grid(True, alpha=0.3)
            ax2.tick_params(axis='x', rotation=45)

            # 保存图表
            chart_title = title or f"A股板块{metric}时间序列分析"
            filepath = self._save_figure(fig, f"time_series_{metric}", chart_title)

            plt.close(fig)
            return filepath

        except Exception as e:
            self.logger.error(f"绘制时间序列趋势图时出错: {str(e)}")
            return ""

    def generate_comprehensive_report(self, all_data: Dict,
                                    report_title: str = "A股板块数据分析综合报告") -> List[str]:
        """
        生成综合报告

        Args:
            all_data: 包含所有分析数据的字典
            report_title: 报告标题

        Returns:
            生成的图表文件路径列表
        """
        self.logger.info("开始生成综合可视化报告...")

        generated_files = []

        try:
            # 1. 时间窗口表现图表
            if 'window_performance' in all_data:
                for window_days, performance_data in all_data['window_performance'].items():
                    if not performance_data.empty:
                        title = f"{report_title} - {window_days}日时间窗口表现"
                        filepath = self.plot_window_performance(performance_data, window_days,
                                                              top_n=15, title=title)
                        if filepath:
                            generated_files.append(filepath)

            # 2. 排名频次图表
            if 'ranking_frequency' in all_data and not all_data['ranking_frequency'].empty:
                title = f"{report_title} - 排名频次统计"
                filepath = self.plot_ranking_frequency(all_data['ranking_frequency'],
                                                     top_n=15, title=title)
                if filepath:
                    generated_files.append(filepath)

            # 3. 冠军分布图表
            if 'champions' in all_data and not all_data['champions'].empty:
                title = f"{report_title} - 冠军分布统计"
                filepath = self.plot_champion_distribution(all_data['champions'],
                                                         top_n=12, title=title)
                if filepath:
                    generated_files.append(filepath)

            # 4. 时间序列趋势图（如果有原始数据）
            if 'raw_data' in all_data and 'top_sectors' in all_data:
                title = f"{report_title} - 时间序列趋势"
                filepath = self.plot_time_series_trends(all_data['raw_data'],
                                                       all_data['top_sectors'][:6],
                                                       title=title)
                if filepath:
                    generated_files.append(filepath)

            # 5. 生成综合对比图表
            if len(generated_files) > 0:
                summary_filepath = self._create_summary_chart(all_data, report_title)
                if summary_filepath:
                    generated_files.append(summary_filepath)

            self.logger.info(f"综合报告生成完成，共生成 {len(generated_files)} 个图表文件")
            return generated_files

        except Exception as e:
            self.logger.error(f"生成综合报告时出错: {str(e)}")
            return generated_files

    def _create_summary_chart(self, all_data: Dict, report_title: str) -> str:
        """
        创建摘要图表

        Args:
            all_data: 所有分析数据
            report_title: 报告标题

        Returns:
            保存的文件路径
        """
        try:
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

            # 1. 不同时间窗口的最佳板块对比
            if 'window_performance' in all_data:
                window_days = []
                best_returns = []
                best_sectors = []

                for days, data in all_data['window_performance'].items():
                    if not data.empty:
                        window_days.append(f"{days}日")
                        best_returns.append(data.iloc[0]['cumulative_return_pct'])
                        best_sectors.append(data.iloc[0]['sector_name'][:8])

                if window_days:
                    colors = plt.cm.viridis(np.linspace(0, 1, len(window_days)))
                    bars = ax1.bar(window_days, best_returns, color=colors, alpha=0.8,
                                  edgecolor='black', linewidth=0.5)

                    # 添加板块名称标签
                    for bar, sector, value in zip(bars, best_sectors, best_returns):
                        ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                                f'{sector}\n{value:.1f}%', ha='center', va='bottom',
                                fontsize=9, fontweight='bold')

                    ax1.set_ylabel('最佳累计收益率 (%)', fontsize=11, fontweight='bold')
                    ax1.set_title('不同时间窗口最佳板块表现', fontsize=12, fontweight='bold')
                    ax1.grid(True, alpha=0.3, axis='y')

            # 2. 冠军vs前10频次对比
            if 'champions' in all_data and 'ranking_frequency' in all_data:
                champions = all_data['champions'].head(10)
                frequency = all_data['ranking_frequency'].head(10)

                if not champions.empty and not frequency.empty:
                    # 合并数据
                    merged = pd.merge(champions[['sector_code', 'sector_name', 'champion_count']],
                                    frequency[['sector_code', 'top10_count']],
                                    on='sector_code', how='inner')

                    if not merged.empty:
                        scatter = ax2.scatter(merged['champion_count'], merged['top10_count'],
                                            c=merged['champion_count'], cmap='Reds',
                                            s=100, alpha=0.7, edgecolors='black', linewidth=0.5)

                        # 添加板块名称标签
                        for idx, row in merged.iterrows():
                            ax2.annotate(row['sector_name'],
                                       (row['champion_count'], row['top10_count']),
                                       xytext=(3, 3), textcoords='offset points',
                                       fontsize=8, alpha=0.8)

                        ax2.set_xlabel('冠军次数', fontsize=11, fontweight='bold')
                        ax2.set_ylabel('前10次数', fontsize=11, fontweight='bold')
                        ax2.set_title('冠军次数 vs 前10频次', fontsize=12, fontweight='bold')
                        ax2.grid(True, alpha=0.3)

            # 3. 板块表现分布直方图
            if 'window_performance' in all_data:
                all_returns = []
                for days, data in all_data['window_performance'].items():
                    if not data.empty:
                        all_returns.extend(data['cumulative_return_pct'].tolist())

                if all_returns:
                    ax3.hist(all_returns, bins=20, alpha=0.7, color='skyblue',
                            edgecolor='black', linewidth=0.5)
                    ax3.axvline(np.mean(all_returns), color='red', linestyle='--',
                               linewidth=2, label=f'平均值: {np.mean(all_returns):.2f}%')
                    ax3.axvline(np.median(all_returns), color='green', linestyle='--',
                               linewidth=2, label=f'中位数: {np.median(all_returns):.2f}%')

                    ax3.set_xlabel('累计收益率 (%)', fontsize=11, fontweight='bold')
                    ax3.set_ylabel('频次', fontsize=11, fontweight='bold')
                    ax3.set_title('板块收益率分布', fontsize=12, fontweight='bold')
                    ax3.legend()
                    ax3.grid(True, alpha=0.3)

            # 4. 数据概览文本
            ax4.axis('off')

            # 生成统计文本
            stats_text = self._generate_stats_text(all_data)
            ax4.text(0.1, 0.9, stats_text, transform=ax4.transAxes, fontsize=11,
                    verticalalignment='top', bbox=dict(boxstyle="round,pad=0.3",
                    facecolor="lightgray", alpha=0.5))

            # 保存图表
            filepath = self._save_figure(fig, "comprehensive_summary",
                                       f"{report_title} - 综合摘要")

            plt.close(fig)
            return filepath

        except Exception as e:
            self.logger.error(f"创建摘要图表时出错: {str(e)}")
            return ""

    def _generate_stats_text(self, all_data: Dict) -> str:
        """生成统计文本"""
        stats = ["📊 数据分析摘要\n"]

        try:
            # 时间窗口统计
            if 'window_performance' in all_data:
                window_count = len(all_data['window_performance'])
                stats.append(f"• 分析时间窗口: {window_count} 个")

                best_overall = None
                best_return = float('-inf')
                for days, data in all_data['window_performance'].items():
                    if not data.empty and data.iloc[0]['cumulative_return_pct'] > best_return:
                        best_return = data.iloc[0]['cumulative_return_pct']
                        best_overall = (days, data.iloc[0]['sector_name'])

                if best_overall:
                    stats.append(f"• 最佳表现: {best_overall[1]} ({best_overall[0]}日, {best_return:.2f}%)")

            # 冠军统计
            if 'champions' in all_data and not all_data['champions'].empty:
                champions = all_data['champions']
                total_champions = len(champions)
                most_champion = champions.iloc[0]
                stats.append(f"• 冠军板块数: {total_champions} 个")
                stats.append(f"• 最多冠军: {most_champion['sector_name']} ({most_champion['champion_count']}次)")

            # 前10统计
            if 'ranking_frequency' in all_data and not all_data['ranking_frequency'].empty:
                frequency = all_data['ranking_frequency']
                most_frequent = frequency.iloc[0]
                stats.append(f"• 最频繁前10: {most_frequent['sector_name']} ({most_frequent['top10_count']}次)")

            # 分析日期
            stats.append(f"\n• 报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        except Exception as e:
            stats.append(f"• 统计信息生成出错: {str(e)}")

        return "\n".join(stats)

    def get_chart_info(self) -> Dict:
        """获取图表生成信息"""
        return {
            'output_dir': str(self.output_dir),
            'chart_config': self.chart_config,
            'supported_formats': ['png', 'jpg', 'pdf', 'svg']
        }
